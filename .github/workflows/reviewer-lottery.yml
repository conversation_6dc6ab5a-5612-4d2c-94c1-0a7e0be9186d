name: "Reviewer lottery"
on:
  pull_request_target:
    types: [opened, ready_for_review, reopened]

jobs:
  review-lottery:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout reviewer-lottery code
      uses: actions/checkout@v4
    - name: Read shared config
      env:
        REVIEWER_LOTTERY_GOLANG: ${{ vars.REVIEWER_LOTTERY_GOLANG }}
      run: echo "$REVIEWER_LOTTERY_GOLANG" > ./.reviewer-lottery-config.yml
    - name: Run reviewer-lottery
      uses: uesteibar/reviewer-lottery@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        config: ./.reviewer-lottery-config.yml
