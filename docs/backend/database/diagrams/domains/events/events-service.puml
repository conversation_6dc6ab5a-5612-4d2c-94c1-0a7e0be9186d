@startuml events-service
  ' uncomment the line below if you're using computer with a retina display
  ' skinparam dpi 300
  !define Table(name,desc) class name as "desc" << (T,#FFAAAA) >>
  ' we use bold for primary key
  ' green color for unique
  ' and underscore for not_null
  !define primary_key(x) <b>x</b>
  !define unique(x) <color:green>x</color>
  !define not_null(x) <u>x</u>
  ' other tags available:
  ' <i></i>
  ' <back:COLOR></color>, where color is a color name or html color code
  ' (#FFAACC)
  ' see: http://plantuml.com/classes.html#More
  hide methods
  hide stereotypes

  title
    events-service
  end title

  package Event {
    enum EventCategory{
      FESTIVAL
      CONCERT
    }

    enum UserRole {
      ADMIN
      EVENT_ADMIN
      PROMOTER
      SECURITY
    }

    entity Event{
      primary_key(id): uuid <<generated>>
      --
       firestore_id: varchar
       is_approved: bool
       is_visible: bool
       title: varchar
       subtitle: varchar
       category: EventCategory
       description: varchar
       checked_in: integer <<deprecated>>
       plan_id: varchar
       total_sales: integer <<deprecated>>
       thumbnail_path: varchar <<deprecated>>
       thumbnail_url: varchar
       cover_path: varchar <<deprecated>>
       cover_url: varchar
       logo_path: varchar <<deprecated>>
       logo_url: varchar
       show_logo_on_banner: bool
       start_date: Date
       end_date: Date
       venue_id: uuid
       promoter_id: uuid
       balance_account_id: varchar
       admission_date: Date
       box_office_opening_date: Date
       tickets_sold_last_three_days: integer <<deprecated>>
       kickback:integer
       chart_key: varchar
       is_draft: bool
       published_date: utc_datetime
      not_null(is_open_end): bool
       deleted_at: utc_datetime
       not_null(inserted_at): utc_datetime
       not_null(updated_at): utc_datetime
    }

    entity EventPermission {
      primary_key(id): uuid <<generated>>
      --
      not_null(event): Event
      ' id from accounts-service
      userDocumentId: varchar
      userId: uuid
      not_null(isInheritedFromSeller): bool <<default: false>>
      not_null(role): [UserRole]
      --
      not_null(insertedAt): DateTime
      not_null(updatedAt): DateTime
    }

    entity Venue{
      primary_key(id): uuid <<generated>>
      --
       firestore_id: varchar
       *name: varchar
       address_id: uuid
       deleted_at: utc_datetime
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }

    entity Fee {
      primary_key(id): uuid <<generated>>
      --
       event_id: uuid
       name: varchar
       tax_rate: float
       unit_price: float
       deleted_at: utc_datetime
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }
  }

  package Accounting <<Frame>> #CCCCCC {
    enum PlatformFeeType {
      PRESALE_FEE
      TRANSACTION_FEE
    }

    enum FeeAmountType {
      ABSOLUTE
      RELATIVE
    }

    enum ChargedEntityType {
      BUYER
      ORGANIZER
      STAGEDATES
    }

    entity PlatformFee {
      primary_key(id): uuid <<generated>>
      --
      * event_id: uuid
      type: PlatformFeeType
      ' Combined index for fixPrice and relativePrice. One of them is null the other one is not null
      ' fix price: e.g. 0.75 EUR
      * fixAmount: Money
      ' relative price: e.g. 0.05 -> 5%
      * relativeAmount: float
      * feeAmountType: FeeAmountType
      chargedEntityType: ChargedEntityType
      priceRangeStart: Money
      priceRangeEnd: Money
      --
      deletedAt: utc_datetime
      * insertedAt: utc_datetime
      * updatedAt: utc_datetime
    }

    PlatformFeeType -- PlatformFee
    FeeAmountType -- PlatformFee
    ChargedEntityType -- PlatformFee
  }


  package Discount {
    enum VoucherType{
      PERCENTAGE
      AMOUNT
    }

    enum QuotaMode{
      RESERVED
      SHARED
    }

    enum VoucherScopeType {
      VARIANT
      TICKET_CATEGORY
      EVENT
      ORGANIZER
    }

    entity Voucher{
      primary_key(id): uuid <<generated>>
      --
      ' decprecated
       firestore_id: varchar
       not_null(active): bool
       not_null(code): varchar
       description: varchar
       not_null(scopeId): uuid
       not_null(scope): VoucherScopeType
       not_null(quota): integer
       not_null(type): VoucherType
       ' deprecated
       not_null(used): integer
       validFrom: utc_datetime
       validUntil: utc_datetime
       not_null(value): integer
       not_null(limitPerRedemption): integer
       deleted_at: utc_datetime
       not_null(insertedAt): utc_datetime
       not_null(updatedUt): utc_datetime
    }

    entity SalesChannel {
      primary_key(id): uuid <<generated>>
      --
      not_null(originalPrice): integer
      not_null(quotaMode) :QuotaMode
      not_null(variant): Variant
      not_null(channelConfig): ChannelConfig
      deletedAt: utc_datetime
      not_null(insertedAt): utc_datetime
      not_null(updatedAt): utc_datetime
    }

    entity ChannelConfig {
      primary_key(id): uuid <<generated>>
      --
      not_null(event_id): uuid
     ' null if used as a SalesChannel, without Seats.io seating plan
      channel_key: uuid
      not_null(token): varchar
      not_null(value): varchar
      not_null(type): varchar
      valid_until: varchar
      ' TODO rename to "name"
      not_null(label): varchar
      description: varchar
      not_null(color): varchar
      ' Stores the amount of objects assigned to a seats.io channel locally at stagedates
      not_null(amount_of_objects): integer
      deleted_at: utc_datetime
      not_null(inserted_at): utc_datetime
      not_null(updated_at): utc_datetime
    }

    entity VariantChannelConfig {
      primary_key(id): uuid
      __
      variant_id: uuid
      channel_config_id: uuid
      ' Used to show a visual hint in the UI about the reduced price. The calculated reduced price is stored in the variant.
      original_price: integer
      ' Maximum number of items to be sold from this channel
      quota: integer
      not_null(inserted_at): utc_datetime
      not_null(updated_at): utc_datetime
      deleted_at: utc_datetime
    }
  }

  package Artist {
    enum ArtistSource {
      SPOTIFY
      SOUNDCLOUD
      YOUTUBE
    }


    entity Artist {
      primary_key(id): uuid <<generated>>
      --
       event_id: uuid
       source: ArtistSource
       label: varchar
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }
  }

  package Variant {
    enum VisibilityStatus {
      INVISIBLE
      VISIBLE
      VISIBLE_WITHOUT_DATE
      VISIBLE_WITHOUT_PRICE
      VISIBLE_WITHOUT_PRICE_AND_DATE
    }

    enum DistributionType{
      REGULAR
      '  In reality guest lists are sales channel with 100% discount and should be refactored in the future
      GUEST_LIST_INVITATION <<deprecated>>
      SALES_CHANNEL
    }

    entity TicketCategory{
      primary_key(id): uuid <<generated>>
      --
      not_null(nam)e: varchar
      description: varchar
      not_null(taxRate): integer
      not_null(eventId): uuid
      not_null(isVisible): boolean
      ' quota for all variants of this ticket category
      not_null(quota): integer
      ' reserved Quota for sales channels or guest lists of this ticket category
      not_null(reservedQuota): integer
      not_null(price): Money
      minAmount: integer
      maxAmount: integer
      --
      deletedAt: utc_datetime
      not_null(insertedAt): utc_datetime
      not_null(updatedAt): utc_datetime
    }

    entity Variant {
      primary_key(id): uuid <<generated>>
      not_null(unitPrice): integer
      ' null for infinity
      quota: integer <<deprecated>>
      ' null if it "always" availble
      availability: Availability
      not_null(ticketCategory): TicketCategory
      not_null(event): Event
      ' sales starts when the sale of the predecessor ends
      predecessor: Variant
      ' default: false
      not_null(soldOutFromReservation): boolean
      not_null(visibilityBeforeSalesStarted): VisibilityStatus
      not_null(visibilityAfterSalesEnded): VisibilityStatus
      not_null(orderNo): integer <<default: 1>>
      not_null(distributionType): DistributionType
      ' can be a sales_channel_id or an invitation_id
      ' null if distributionType is REGULAR
      distributionTypeId: uuid
      minAmount: integer
      maxAmount: integer
      --
      deletedAt: utc_datetime
      not_null(insertedAt): utc_datetime
      not_null(updatedAt): utc_datetime
    }
  }

  package MLP {
    entity MultiLevelPricing {
      primary_key(id): uuid
      not_null(name): varchar
      not_null(event): Event
      notes: varchar
      --
      deletedAt: utc_datetime
      not_null(insertedAt): utc_datetime
      not_null(updatedAt): utc_datetime
    }

    entity MultiLevelPricingModifier {
      primary_key(id): uuid
      ' e. g. "Senior", "Child"
      not_null(label): varchar
      ' e. g. "discount: 10%"
      description: varchar
      ' `percentage` and `flat` are the new defined default names for attributes in this context. All other namings
      ' will be refactored some day
      ' Combined index for fixModifier and relativeModifier. One of them is null the other one is not null
      flatModifier: Money
      percentageModifier: float 
      minAmount: integer
      maxAmount: integer
      not_null(mlp_id): uuid
      not_null(is_default): boolean
      not_null(order_no): integer
      --
      deletedAt: utc_datetime
      not_null(insertedAt): utc_datetime
      not_null(updatedAt): utc_datetime
    }

    entity MultiLevelPricingModifierVariant{
      primary_key(multiLevelPricingModifier): MultiLevelPricingModifier
      primary_key(variant): Variant
    }
  }

  package Offer {
    entity Availability {
      primary_key(id): uuid
      name: varchar
      validFrom: DateTime
      validUntil: DateTime
      * onlyInternalUse: boolean
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }
  }

  package SeatsIO
  {
    entity SeatCommentGroupObjects{
      *id: uuid <<generated>>
      --
      *seat_comment_group_id: uuid
      *object_id: uuid
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity SeatCommentGroups{
      *id: uuid <<generated>>
      --
      *event_id: uuid
      *group_key: uuid
      *name: varchar
      created_by: uuid
      created_by_document_id: varchar
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity SeatComments{
      *id: uuid <<generated>>
      --
      *seat_comment_group_id: uuid
      created_by: uuid
      created_by_document_id: varchar
      *history_id: uuid
      *text: varchar
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity SeatsioWorkspaceKeys{
      *id: uuid <<generated>>
      --
      *promoter_id: uuid
      public_key: varchar
      private_key: varchar
      public_test_workspace_key: varchar
      private_test_workspace_key: varchar
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }
  }

  package Payment {
    enum PayoutStatus{
      INITIATED
      AUTHORISED
      BOOKED
      CREDITED
      RETURNED
      FAILED
    }

    entity PayoutTransaction {
      primary_key(id): uuid <<generated>>
      --
       event_id: uuid
       payment_instrument: varchar
       balance_account_id: varchar
       amount: integer
       created_by: uuid
       psp: varchar
       currency: varchar
       psp_reference: varchar
       status: PayoutStatus
       psp_result: map
       deleted_at: utc_datetime
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }

    entity TransactionHistory {
      primary_key(id): uuid <<generated>>
      --
       transaction_id: uuid
       state: varchar
       inserted_at: utc_datetime
    }
  }


  package Tracking {
    enum tracking_pixel_type{
      meta
    }

    enum tracking_pixel_credential_type{
      meta_pixel_id
      meta_access_token
    }

    entity TrackingPixelCredential{
      *id: uuid <<generated>>
      --
      *tracking_pixel_id :uuid
      *type: tracking_pixel_credential_type
      *value: varchar
      valid_until: utc_datetime
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity TrackingPixel{
      *id: uuid <<generated>>
      --
      *event_id: uuid
      *label: varchar
      *type: tracking_pixel_type
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity TrackingLink {
      primary_key(id): uuid <<generated>>
      --
       event_id: uuid
       label: varchar
       target_url: varchar
       url: varchar
       deleted_at: utc_datetime
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }
  }

  package Location {
    entity Address{
      *id: uuid <<generated>>
      --
      street_address: varchar
      postal_code: varchar
      locality: varchar
      region: varchar
      *country_id: uuid
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity Country{
      *id: uuid <<generated>>
      --
      *iso: varchar <<unique>>
      *iso3: varchar <<unique>>
      *country: varchar
      continent: varchar
      currency_code: varchar
      currency_name: varchar
      phone_prefix: varchar
      languages: [varchar]
      *is_supported: boolean <<default: false>>
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity GeoCoordinate {
      primary_key(id): uuid <<generated>>
      --
       address_id: uuid
       latitude: float
       longitude: float
       *inserted_at: utc_datetime
       *updated_at: utc_datetime
    }

  }

  package Organizer {
    enum promoter_entity_type{
      INDIVIDUAL
      COMPANY
      PRIVATE
    }

    entity Promoter{
      primary_key(id): uuid <<generated>>
      --
      firestore_id: varchar
      not_null(display_name): varchar
      given_name: varchar
      family_name: varchar
      company_name: varchar
      store_url: varchar
      account_holder_id: varchar
      balance_account_id: varchar
      legal_entity_id: varchar
      tax_id: varchar
      entity_type: promoter_entity_type
      address_id: uuid
      not_null(owner_id): uuid
      not_null(created_by): uuid
      not_null(is_verified): boolean <<default: false>>
      deleted_at: utc_datetime
      not_null(inserted_at): utc_datetime
      not_null(updated_at): utc_datetime
    }

    entity PromoterEmployee{
      *id: uuid <<generated>>
      --
      *promoter_id: uuid
      *given_name: varchar
      *family_name: varchar
      gender: GenderType
      *legal_entity_id: varchar
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity PromoterUser{
      *id: uuid <<generated>>
      --
      user_id: uuid
      user_document_id: string
      *promoter_id: uuid
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }
  }

  package Donation {
    entity DonationEvent {
      primary_key(id): uuid <<generated>>
      --
      donation_recipient_id: uuid
      event_id: uuid
      amount: integer
      type: varchar
    }

    entity DonationRecipientEmployee{
      *id: uuid <<generated>>
      --
      *donation_recipient_id: uuid
      given_name: varchar
      family_name: varchar
      legal_entity_id: varchar
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity DonationRecipient{
      *id: uuid <<generated>>
      --
      *display_name: varchar
      *given_name: varchar
      *family_name: varchar
      *account_holder_id: varchar
      *balance_account_id: varchar
      *legal_entity_id: varchar
      *country_id: uuid
      *is_verified: boolean <<default: false>>
      *is_active: boolean <<default: false>>
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }

    entity DonationRecipientUser{
      *id: uuid <<generated>>
      --
      *user_id: uuid
      user_document_id: string
      *donation_recipient_id: uuid
      deleted_at: utc_datetime
      *inserted_at: utc_datetime
      *updated_at: utc_datetime
    }
  }

  package Counter {
    entity EventCounter {
      primary_key(id): uuid <<generated>>
      --
      * event: Event
      totalSales: integer
      soldTickets: integer
      soldExtras: integer
      soldForPopularEvents: integer
      totalCheckedIn: integer
      redeemedExtras: integer
      lastSyncAt: DateTime
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
    }

    entity VariantCounter{
      primary_key(id): uuid <<generated>>
      --
      * variant: Variant
      sold: integer
      totalSales: integer
      lastSyncAt: DateTime
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
    }

    entity VoucherCounter {
      primary_key(id): uuid <<generated>>
      --
      * voucher: Voucher
      used: integer
      lastSyncAt: DateTime
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
    }

    entity TicketCategoryCounter {
      primary_key(ticketCategory): TicketCategory
      totalSales: Money
      ' count of "tickets" from regular sale or sales channel
      soldItems: integer
      soldReservedItems: integer
      ' count of valid "tickets" without a sale, e. g. guestList, manually created (RockHard)
      existingItems: integer
-      exisingReservedItems: integer
+      existingReservedItems: integer
      lastSyncAt: DateTime
    }
  }

  package Guestlist {
    enum InvitationStatus {
      ACCEPTED
      PENDING
      REJECTED
    }

    enum InvitationApprovalRequestStatus {
      ACCEPTED
      PENDING
      REJECTED
    }

    enum ChannelType {
      EMAIL
      LINK
    }

    enum ActorType {
      PROMOTER
      USER
    }

    entity Guestlist {
      primary_key(id): uuid <<generated>>
      --
      event: Event
      variant: Variant
      quota: integer
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity GuestlistHistory {
      primary_key(id): uuid <<generated>>
      --
      guestlist: Guestlist
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity Invitation {
      primary_key(id): uuid <<generated>>
      --
      guestlist: Guestlist
      emailReceiver: PersonalInformation
      variant: Variant
      secret: varchar
      quota: integer
      validFrom: DateTime
      validUntil: DateTime
      maxRequestsPerEmail: integer
      isAutoApprov: boolean
      status: InvitationStatus
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity InvitationApprovalRequest{
      primary_key(id): uuid <<generated>>
      --
      invitation: Invitation
      inivtationLink: InvitationLink
      ' order_id from orders-service
      order: Order
      variant: Variant
      ' userId or userDocumentId from accounts-service
      user: User
      guest: PersonalInformation
      status: InvitationApprovalRequestStatus
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity InvitationAttendee {
      primary_key(id): uuid <<generated>>
      --
      invitationApprovalRequest: InvitationApprovalRequest
      personalInformation: PersonalInformation
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity InvitationHistory {
      primary_key(id): uuid <<generated>>
      --
      invitation: Invitation
      channel: ChannelType
      ' userId or userDocumentId from accounts-service
      actor: User
      actorType: ActorType
      * statusBefore: InvitationStatus
      * statusAfter: InvitationStatus
      metadata: json
      --
      * insertedAt: DateTime
      deletedAt: DateTime
    }

    entity InvitationLink {
      primary_key(id): uuid <<generated>>
      --
      invitation: Invitation
      maxTicketsPerRedemption: integer
      isActive: boolean
      url: varchar
      target: varchar
      description: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }
  }

  package PersonalInformation {
     enum GenderType{
       0: unknown
       1: male
       2: female
       9: not applicable
     }

    entity PersonalInformation {
      primary_key(id): uuid <<generated>>
      --
      ' userId or userDocumentId from accounts-service
      user: User
      gender: GenderType
      givenName: varchar
      familyName: varchar
      phoneNumber: varchar
      email: varchar
      birthdate: Date
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }
  }

  package Tickets <<Frame>> #CCCCCC {
    enum ImportedTicketStatusType {
      ACTIVE
      USED
      UNUSED
    }

    entity ImportedTicket {
      primary_key(id): uuid <<generated>>
      not_null(event): Event
      externalId: varchar
      not_null(provider): varchar
      not_null(code): varchar
      externalOrderId: varchar
      attendeeName: varchar
      "label": varchar
      not_null(importedBy): uuid
      not_null(insertedAt): DateTime
    }

    entity ImportedTicketStatus {
      not_null(importedTicket): ImportedTicket
      statusBefore: ImportedTicketStatusType
      not_null(statusAfter): ImportedTicketStatusType
      changedBy: uuid
      not_null(insertedAt): DateTime
    }
  }

  ' ***************************************************************************************
  ' BookingEngine v2
  ' ***************************************************************************************

  package products <<Frame>> #CCCCCC {
    enum ProductType {
      TICKET
      TOKEN
      PHYSICAL_GOOD
    }

    entity Product {
      primary_key(id): uuid
      ' Needed to check if we need to generate a ticket, token or in future something else
      * type: ProductType
      * basePrice: Money
      seatsCategory: map
      * isValid: boolean
      ageRestriction: int
      * allowAdmission: boolean
      ' eg: name on a shirt
      * allowPersonalization: boolean
      position: int
      pictureUrl: varchar
      * isAddon: boolean
      ' null if there is no quota, eg: drinks flatrate
      quota: integer
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity EventProduct {
      primary_key(id): uuid
      * event: Event
      * product: Product
      ' quota: - defines the overall amount of a good (physical or digital) that is availble in the scope)
      '        - All other quota must respect the global quota defined in this property.
      '        - Can be `null` if no quota should be applied
      quota: Integer
      * tax: Tax
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity ProductVariant {
      primary_key(id): uuid
      * product: Product
      description: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity HardTicketConfig{
      primary_key(id): uuid
      * variant: ProductVariant
      name: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity HardTicketStrips{
      primary_key(id): uuid
      * hardTicketConfig: HardTicketConfig
      iconUrl: varchar
      text: varchar
      ' unique together with hardTicketConfig
      * position: integer
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity HardTicketAdvertising {
      primary_key(id): uuid
      * hardTicketConfig: HardTicketConfig
      iconUrl: varchar
      text: varchar
      ' unique together with hardTicketConfig
      *  position
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity Category {
      primary_key(id): uuid
      * name: varchar
      * isVisible: boolean
      description: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity ProductCategory {
      primary_key(id): uuid
      * product: Product
      * category: Category
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity ProductOption {
      primary_key(id): uuid
      * product: Prduct
      * name: varchar <<unique>>
      * isDefault: boolean
      description: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }
  }

  package Entrance {
    entity EntranceArea {
      primary_key(id): uuid
      * name: varchar(20)
      description: varchar(200)
      * eventId: uuid
      ' userDocumentId: - on creation an internally managed user is created. A scanning device
      '           can impersonate this user and inherit its permissions.
      * userDocumentId: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity Device {
      primary_key(id): uuid
      * name: varchar(20)
      * eventId: uuid
      ' userDocumentId: - on creation an internally managed user is created. A scanning device
      '           can impersonate this user and inherit its permissions.
      * userDocumentId: varchar
      --
      * insertedAt: DateTime
      * updatedAt: DateTime
      deletedAt: DateTime
    }

    entity EntranceAreaValidationPermission {
      ' composite PK
      primary_key(userDocumentId): varchar
      primary_key(entranceAreaId): uuid
      --
      * insertedAt: DateTime
    }

    entity EntranceAreaTicketCategory {
      ' composite PK
      primary_key(ticketCategoryId): uuid
      primary_key(entranceAreaId): uuid
      --
      * insertedAt: DateTime
    }
  }

  ' TODO: move this into the accounting package
  Voucher "n" <--> "1" Event.Event
  Voucher "n" <--> "1" Variant.Variant
  Voucher "n" <--> "1" TicketCategory
  Voucher "n" <--> "1" Promoter
  Event.Event "n" <--> "1" Venue
  Event.Event "n" <--> "1" Promoter
  Event.Event "1" <--> "n" TrackingPixel
  TrackingPixel "1" <--> "n" TrackingPixelCredential
  tracking_pixel_credential_type "1" <--> "n" TrackingPixelCredential
  tracking_pixel_type "1" <--> "n" TrackingPixel
  PayoutTransaction "n" <--> "1" Event.Event
  TransactionHistory "n" <--> "1" PayoutTransaction
  Artist.Artist "n" <--> "1" Event.Event
  Fee "n" <--> "1" Event.Event
  TrackingLink "n" <--> "1" Event.Event
  SalesChannel "n" <--> "1" Variant
  SalesChannel "n" <--> "1" ChannelConfig
  ChannelConfig "n" <--> "1" VariantChannelConfig
  PayoutTransaction "n" <--> "1" PromoterUser
  SeatCommentGroups "n" <--> "1" Event.Event
  SeatCommentGroupObjects "n" <--> "1" SeatCommentGroups
  SeatComments "n" <--> "1" SeatCommentGroups
  ChannelConfig "n" <--> "1" Event.Event
  DonationEvent "n" <--> "1" Event.Event
  DonationEvent "n" <--> "1" DonationRecipient
  Address "1" <--> "n" Venue
  Address "1" <--> "1" GeoCoordinate
  Country "1" <--> "n" Address
  Country "1" <--> "n" DonationRecipient
  DonationRecipientUser "1" <--> "n" DonationRecipient
  DonationRecipientEmployee "1" <--> "n" DonationRecipient
  Address "1" <--> "n" Promoter
  PromoterUser "1" <--> "n" Promoter
  PromoterEmployee "1" <--> "n" Promoter
  promoter_entity_type "1" <--> "n" Promoter
  SeatsioWorkspaceKeys "0..1" <--> "1" Promoter


  EventPermission "n" <--> "1" Event.Event

  ' Accounting
  Event.Event "1" <--> "n" Accounting.PlatformFee

  ' Variant / Sales
  Variant.Variant "n" <--> "1" Event.Event
  Variant.Variant "n" <--> "1" Variant.TicketCategory
  Variant.Variant "n" <--> "1" Offer.Availability
  Variant.Variant "n" <--> "1" Variant.Variant
  Variant.Variant "n" <--> "1" VariantChannelConfig
  Variant.TicketCategory "n" <--> "1" Event.Event

  ' Guestlist
  Guestlist.Guestlist "n" <--> "1" Event.Event
  Guestlist.Guestlist "n" <--> "1" Variant.Variant
  Guestlist.Guestlist "1" <--> "n" Guestlist.GuestlistHistory
  Guestlist.Guestlist "1" <--> "n" Guestlist.Invitation
  Guestlist.Invitation "n" <--> "1" Variant.Variant
  Guestlist.Invitation "n" <--> "1" PersonalInformation.PersonalInformation
  Guestlist.Invitation "1" <--> "n" Guestlist.InvitationApprovalRequest
  Guestlist.Invitation "1" <--> "n" Guestlist.InvitationHistory
  Guestlist.Invitation "1" <--> "n" Guestlist.InvitationLink
  Guestlist.InvitationLink "1" <--> "n" Guestlist.InvitationApprovalRequest
  Guestlist.InvitationApprovalRequest "n" <--> Variant.Variant
  Guestlist.InvitationApprovalRequest "n" <--> PersonalInformation.PersonalInformation
  Guestlist.InvitationApprovalRequest "1" <-->  "n" Guestlist.InvitationAttendee
  Guestlist.InvitationAttendee "n" <--> "1" PersonalInformation.PersonalInformation

  ' Connect Enums to Entities
  Event.Event -- Event.EventCategory
  EventPermission -- UserRole
  ArtistSource -- Artist.Artist
  VoucherType -- Voucher
  VoucherScopeType -- Voucher
  GenderType -- PromoterEmployee
  PayoutStatus -- PayoutTransaction
  Invitation -- InvitationStatus
  InvitationApprovalRequest -- InvitationApprovalRequestStatus
  InvitationHistory -- ChannelType
  InvitationHistory -- ActorType
  InvitationHistory -- InvitationStatus
  PersonalInformation.PersonalInformation -- GenderType
  VisibilityStatus -- Variant
  DistributionType -- Variant
  QuotaMode -- SalesChannel
  Tickets.ImportedTicketStatusType -- Tickets.ImportedTicketStatus

  ' Counter
  EventCounter "0..1" <--> "1" Event.Event
  VariantCounter "0..1" <--> "1" Variant.Variant
  VoucherCounter "0..1" <--> "1" Voucher
  TicketCategoryCounter "0..1" <--> "1" TicketCategory


  ' Product may be offered in multiple in varying quantities
  EventProduct "n" <--> "1" Event.Event
  EventProduct "n" <--> "1" Product

  ' Product
  Product -- ProductType

  Product "1" <--> "n" ProductVariant
  Product "1" <--> "n" ProductOption
  Product "1" <--> "n" ProductCategory
  ProductCategory "n" <--> "1" Category

  Entrance.EntranceAreaTicketCategory "n" <--> "1" Variant.TicketCategory
  Entrance.EntranceAreaTicketCategory "n" <--> "1" Entrance.EntranceArea
  Entrance.EntranceAreaValidationPermission "n" <--> "1" Entrance.EntranceArea
  Entrance.Device "n" <--> "1" Event.Event

  ' MLP
  Event.Event "1" <--> "n" MLP.MultiLevelPricing
  MLP.MultiLevelPricing "1" <--> "n" MLP.MultiLevelPricingModifier
  MLP.MultiLevelPricingModifier "1" <--> "n" MLP.MultiLevelPricingModifierVariant
  MLP.MultiLevelPricingModifierVariant "1" <--> "1" Variant.Variant 

  ' ImportedTickets
  Event.Event "1" <--> "n" Tickets.ImportedTicket
  Tickets.ImportedTicket "1" <--> "n" Tickets.ImportedTicketStatus
  legend
    v1.3.0
  end legend

@enduml
