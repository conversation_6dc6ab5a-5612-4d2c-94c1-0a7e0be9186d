@startuml orders-service
  ' uncomment the line below if you're using computer with a retina display
  ' skinparam dpi 300
  !define Table(name,desc) class name as "desc" << (T,#FFAAAA) >>
  ' we use bold for primary key
  ' green color for unique
  ' and underscore for not_null
  !define primary_key(x) <b>x</b>
  !define unique(x) <color:green>x</color>
  !define not_null(x) <u>x</u>
  ' other tags available:
  ' <i></i>
  ' <back:COLOR></color>, where color is a color name or html color code
  ' (#FFAACC)
  ' see: http://plantuml.com/classes.html#More
  hide methods
  hide stereotypes

  legend right
    Legend:
    | primary_key(Primary Key) |
    | unique(Unique) |
    | not_null(Not Null) |
  end legend

  title
    orders-service
  end title


  enum OrderStatus{
    PENDING
    AWAITING_PAYMENT
    PAID
    FAILED
    FRAUD
    REFUND_PENDING
    REFUNDED
    TIMEDOUT
    DEFRAUDED
    CREATED
  }

  enum TicketStatus {
    INVITATION_REJECTED
    PENDING
    ACTIVE
    USED
    UNUSED
    REFUNDED
    DEFRAUDED
    TIMEDOUT
    FAILED
    CREATED
  }

  enum GenderType{
    0: unknown
    1: male
    2: female
    9: not applicable
  }

  enum PayinStatus {
    PREINIT
    FINALIZING
    SUCCESS
    FAILURE
  }

  enum PromoterBillStatus {
    PENDING
    PAID
  }

  enum ResetTicketCategoryStatus {
    DONE
    FAILED
    PENDING
    STARTED
  }

  enum TicketGroupType {
    BOOTH
    TABLE
  }

  enum RefundStatus {
    INITIATED
    COMPLETED
    FAILED
    CANCELLED
  }

  enum RefundReason {
    FAILURE_OR_GOODWILL_PROMOTER
    FAILURE_STAGEDATES
  }

  enum QuotaMode{
    RESERVED
    SHARED
  }

  enum DistributionType{
    REGULAR
    ' guest lists are sales channel with 100% discount
    GUEST_LIST_INVITATION <<deprecated>>
    SALES_CHANNEL
  }


  'Note that when migrating from firestore, we will use the existing IDs as the primary key (id) in postgres.
  'This is possible because the ID of the order document in firestore is already a UUID.
  entity Order{
    * id: uuid <<firestore_id || generated>>
    --
    billing_address_id: uuid <<Address>>
    email: varchar
    paid_date: utc_datetime
    payer_email: varchar
    *status: OrderStatus
    submitted_date: utc_datetime
    bill_id: uuid <<Bill>>
    created_by: uuid <<PersonalInformation>>
    deleted_at: utc_datetime
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
  }

  entity OrderHistory {
    * id: uuid <<generated>>
    --
    * order_id: uuid <<Order>>
    actor_id: uuid <<User>>
    actor_document_id: varchar <<User>>
    actor_type: varchar
    status_before: OrderStatus
    status_after: OrderStatus
    metadata: json
    inserted_at: utc_datetime
  }

  /'
  Create or reuse new entity in order service
  user_profiles from accounts-service?
  The latter has a mandatory user_id that we don't have for guests.
  '/
  entity PersonalInformation {
    *id: uuid <<generated>>
    --
    user_id: uuid
    gender: GenderType
    given_name: varchar
    family_name: varchar
    phone_number: varchar <<unique>>
    birthdate: date
    country_iso: varchar
    postal_code: varchar
    city: varchar
    deleted_at: utc_datetime
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
  }

  entity Bill {
    primary_key(id): uuid <<generated>>
    --
    not_null(discount): integer
    not_null(donation): integer
    not_null(fee): integer
    not_null(presale_fee): integer
    presale_fee_tax: integer
    not_null(system_fee): integer
    system_fee_tax: integer
    not_null(tax): integer
    not_null(total): integer
    not_null(promoter_total): integer
    promoter_net_total: integer
    promoter_tax: integer
    promoter_kickback: integer
    promoter_kickback_tax: integer
    promoter_tax_rate: integer
    stagedates_tax_rate: integer
    not_null(payment_method_fee): integer
    not_null(payment_method_fee_tax): integer
    not_null(inserted_at): utc_datetime
    not_null(updated_at): utc_datetime
  }

  entity PromoterBill {
    *id: uuid <<generated>>
    --
    *ticket_id: uuid <<Ticket>>
    event_id: uuid <<Event>>
    event_document_id: varchar
    amount: integer
    tax: integer
    currency: varchar
    status: PromoterBillStatus
    inserted_at: utc_datetime
    updated_at: utc_datetime
  }

  entity Address {
    * id: uuid <<generated>>
    --
    formatted: varchar
    street_address: varchar
    postal_code: integer
    locality: varchar
    region: varchar
    * country_id: uuid <<Country>>
    * is_business: boolean <<default: false>>
    company: varchar
    vat_id: varchar
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
  }

  entity Country{
    *id: uuid <<generated>>
    --
    *iso: varchar <<unique>>
    *iso3: varchar <<unique>>
    *country: varchar
    continent: varchar
    currency_code: varchar
    currency_name: varchar
    phone_prefix: varchar
    languages: [varchar]
    *is_supported: boolean <<default: false>>
    flagEmoji: varchar(3)
    deleted_at: utc_datetime
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
    }

  entity Ticket{
    primary_key(id): uuid <<generated>>
    --
    event_id: uuid
    event_document_id: varchar
    variant_id: uuid
    category_id: uuid
    not_null(is_checkin_allowed): boolean
    purchase_date: utc_datetime
    owner_id: uuid <<PersonalInformation>>
    attendee_id: uuid <<PersonalInformation>>
    voucher_id: uuid
    ticket_status: TicketStatus
    seat: varchar
    channel_config_id: uuid
    'seats: seat_id
    not_null(distribution): DistributionType
    distributionTypeId: uuid
    not_null(inserted_at): utc_datetime
  }

  entity TicketQuota{
    ' SalesChannelId, GuestListId or TicketCategoryId
    primary_key(typeId): uuid
    not_null(distribution): DistributionType
    not_null(totalQuota): integer
    not_null(blockedQuota): integer
    not_null(quotaMode): QuotaMode
    ' computed columns: count sold and reserved tickets
    countTotal: integer <<computed>>
    countBlocked: integer <<computed>>
  }

  entity TransactionHistory {
    * id: uuid <<generated>>
    --
    transaction_id: uuid <<PayinTransaction>>
    state: varchar
    psp_result: blob
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
  }

  entity ScanCode {
    primary_key(scanCode): string
    not_null(ticket_id): uuid <<Ticket>>
    not_null(inserted_at): utc_datetime
    not_null(updated_at): utc_datetime
  }

  entity PayinTransaction {
    * id: uuid <<generated>>
    --
    *amount: integer
    *currency: varchar
    *order_id: uuid <<Order>>
    payment_method: varchar
    *psp: varchar
    psp_reference: varchar
    psp_result: blob
    *status: PayinStatus
    *inserted_at: utc_datetime
    *updated_at: utc_datetime
  }

  entity RefundTransaction {
    * id: uuid <<generated>>
    --
    amount: integer
    actor_id: varchar
    * comment: varchar
    currency: varchar
    ticket_id: uuid <<Ticket>>
    payment_method: varchar
    psp: varchar
    psp_reference: varchar
    psp_result: blob
    status: RefundStatus
    reason: RefundReason
    support_ticket_id: varchar
    inserted_at: utc_datetime
  }

   entity SwapTransaction {
    * id: uuid <<generated>>
      --
      attendee_id: uuid <<PersonalInformation>>
    * parent_order_id: uuid <<Order>>
    * parent_ticket_id: uuid <<Ticket>>
    * original_order_id: uuid <<Order>>
    * original_ticket_id: uuid <<Ticket>>
    * new_ticket_id: uuid <<Ticket>>
    * inserted_at: utc_datetime
   }


  entity InvitationOrder {
    * id: uuid <<generated>>
    --
    * order_id: uuid <<Order>>
    * guestlist_id: uuid <<Guestlist>>
    * invitation_id: uuid <<Invitation>>
    deleted_at: utc_datetime
    * inserted_at: utc_datetime
    * updated_at: utc_datetime
  }

  entity EmailBlacklist {
    * id: uuid <<generated>>
    --
    * email: varchar
    inserted_by_document_id: varchar <<User>>
    inserted_by: uuid <<User>>
    comment: varchar
    deleted_at: utc_datetime
    inserted_by: utc_datetime
    updated_at: utc_datetime
  }

  entity ResetTicketCategory {
    * id: uuid <<generated>>
    --
    event: Event
    ticketCategory: ticketCategory
    resetAt: DateTime
    resetStatus: ResetTicketCategoryStatus
    --
    * insertedAt: DateTime
    * updatedAt: DateTime
    deletedAt: DateTime
  }

  entity TicketHistory {
    primary_key(id): uuid <<generated>>
    --
    ticket: Ticket
    ' userId or userDocumentId from accounts-service
    actor: User
    actorType: varchar
    ' If the scanner device provides location information during ticket
    ' validation, the location is set to the entrance the ticket was validated at.
    ' 
    ' Type of the referenced location, currently only supports EntranceArea
    locationType: EntranceArea
    locationId: uuid
    * statusBefore: TicketStatus
    * statusAfter: TicketStatus
    metadata: json
    --
    * insertedAt: DateTime
  }

  entity TicketGroup {
    * id: uuid <<generated>>
    --
    * event: Event
    variant: Variant
    * category: TicketCategory
    * amount: integer
    * bill: Bill
    * type: TicketGroupType
    * label: varchar
    --
    * insertedAt: DateTime
    * updatedAt: DateTime
    deletedAt: DateTime
  }

  entity OrderTicket {
    * id: uuid <<generated>>
    --
    * order: Order
    * ticket: Ticket
      bill: Bill
      donation_amount: integer
      donation_recipient_id: uuid
    * insertedAt: DateTime
  }

  ' Template table to validate address data and copy information into the adresses and personalInfomration tables.
  ' This table stands alone and is not connected to any other table.
  entity PostalCode {
    * postalCode: varchar
    * countryIso: varchar
    * city: varchar
    * insertedAt: DateTime
  }

  package Reports {
    entity SettledPayments {
      primary_key(id): uuid <<generated>>
      company_account: varchar
      merchant_account: varchar
      psp_reference: varchar
      merchant_reference: varchar
      payment_method: varchar
      creation_date: utc_datetime
      timezone: varchar
      type: varchar
      modification_references: varchar
      gross_currency: varchar
      gross_debit: decimal
      gross_credit: decimal
      exchange_rate: decimal
      net_currency: varchar
      net_debit: decimal
      net_credit: decimal
      commission: decimal
      markup: decimal
      scheme_fees: decimal
      interchange: decimal
      payment_method_variant: varchar
      modification_merchant_reference: varchar
      batch_number: integer
      not_null(inserted_at): utc_datetime
      not_null(updated_at): utc_datetime
    }
  }


  package Accounting {
    enum PlatformFeeType {
      FUTURE_DEMAND_FEE
      PAYMENT_METHOD_FEE
      PRESALE_FEE
      SHIPPING_FEE
      TRANSACTION_FEE
    }

    enum FeeAmountType {
      ABSOLUTE
      RELATIVE
    }

    enum ChargedEntityType {
      BUYER
      ORGANIZER
    }

    enum ChargeBaseType {
      UNIT_PRICE
      ORGANIZER_TOTAL
      ORDER_TOTAL
    }

    ' PaymentMethod like used in Adyen
    enum PaymentMethod {
      APPLEPAY
      GOOGLEPAY
      KLARNA_PAYNOW
      PAYBYBANK
      PAYPAL
      SCHEME
    }

    entity PlatformFee {
      ' primary key will be used in the second iteration to add the platform fees in the bill
      primary_key(id): uuid <<generated>>
      not_null(type): PlatformFeeType
      not_null(feeAmountType): FeeAmountType
      ' Combined index for fixAmount and relativeAmount. One of them is null the other one is not null
      ' fix price: e.g. 0.75 EUR
      fixAmount: Money
      ' relative price: e.g. 0.05 -> 5%
      relativeAmount: decimal
      ' default: BUYER
      not_null(chargedEntity): ChargedEntityType
      ' null if the  fee is not limited to a specific range
      priceRangeStart: Money
      priceRangeEnd: Money
      ' null if the fee is not limited to a specific payment method
      paymentMethod: PaymentMethod
      not_null(chargedBase): ChargedBaseType
      not_null(validFrom): utc_datetime
      validUntil: utc_datetime
      not_null(balanceAccountId): varchar
      comment: varchar
      not_null(insertedBy): varchar
      not_null(insertedAt): utc_datetime
    }

    PlatformFee -- PlatformFeeType
    PlatformFee -- FeeAmountType
    PlatformFee -- ChargedEntityType
    PlatformFee -- ChargeBaseType
    PlatformFee -- PaymentMethod
  }


  Ticket ||..|{ PromoterBill
  Order ||..|{ OrderHistory
  Order ||..|{ Bill
  Order ||..|{ Address
  Order ||..|{ PersonalInformation
  Ticket ||..|{ PersonalInformation
  Order ||..|{ PayinTransaction
  Country }o..|| Address
  Order |o..|{ InvitationOrder


  'Order ||..|{ OrderAttendee
  'Ticket ||..|{ OrderAttendee


  PayinTransaction ..o{ TransactionHistory


  'Zero or One 	|o--
  'Exactly One 	||--
  'Zero or Many 	}o--
  'One or Many 	}|--

  Order -- OrderStatus
  OrderHistory -- OrderStatus
  Ticket -- TicketStatus
  TicketHistory -- TicketStatus
  ResetTicketCategory -- ResetTicketCategoryStatus
  TicketGroup -- TicketGroupType
  PersonalInformation -- GenderType
  PayinTransaction -- PayinStatus
  PromoterBill -- PromoterBillStatus
  Ticket -- DistributionType
  TicketQuota - QuotaMode
  TicketQuota - DistributionType

  Ticket "1" <--> "n" TicketHistory
  Ticket "1" <--> "n" ScanCode
  TicketGroup "1" <--> "n" Bill


  Order ||..|{ OrderTicket

  Ticket |o-- OrderTicket

  OrderTicket |o-- Bill

  Ticket |o-- RefundTransaction

  Ticket |o-- SwapTransaction

  Order }o-- SwapTransaction

  SwapTransaction ||-- PersonalInformation
@enduml
