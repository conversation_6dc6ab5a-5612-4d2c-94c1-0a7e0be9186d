defmodule EventsService.MixProject do
  use Mix.Project

  @version "4.48.0"
  def project do
    [
      app: :events_service,
      version: @version,
      elixir: "~> 1.17",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      releases: [
        events_service: [
          applications: [opentelemetry: :temporary]
        ]
      ]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {EventsService.Application, []},
      extra_applications: [:logger, :runtime_tools, :db_seeds]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/factories", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    # styler:sort
    pub_deps = [
      {:broadway, "~> 1.0"},
      {:broadway_cloud_pub_sub, "~> 0.8"},
      {:cors_plug, "~> 3.0"},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:csv, "~> 3.2"},
      {:db_seeds, "~> 0.0"},
      {:dialyxir, "~> 1.0", only: [:dev], runtime: false},
      # Pinning ecto version to 3.12.6 since later versions make the service uncompilable
      {:ecto, "3.12.6"},
      {:ecto_commons, "~> 0.3"},
      {:ecto_soft_delete, "~> 2.1"},
      # Pinning ecto_sql version to 3.12.1 since later versions make the service uncompilable
      {:ecto_sql, "3.12.1"},
      {:ex_machina, "~> 2.8", only: :test},
      {:ex_money, "~> 5.16"},
      {:ex_money_sql, "~> 1.11"},
      {:faker, "~> 0.18", only: :test},
      {:finch, "~> 0.10", override: true},
      {:gettext, "~> 0.20"},
      {:google_api_big_query, "~> 0.76"},
      {:google_api_pub_sub, "== 0.38.0"},
      {:google_api_storage, "~> 0.37"},
      {:goth, "~> 1.4"},
      {:html_sanitize_ex, "~> 1.4"},
      {:httpoison, "~> 2.2"},
      {:icalendar, "~> 1.1"},
      # override to handle conflict with ex_firebase_auth
      {:jason, "~> 1.4", override: true},
      {:joken, "~> 2.5"},
      {:json, "~> 1.4"},
      {:logger_json, "~> 6.0"},
      {:mogrify, "~> 0.9"},
      # fix Oban to don't use Elixir 1.18
      {:oban, "2.18.3"},
      {:open_api_spex, "~> 3.4"},
      {:opentelemetry, "~> 1.3"},
      {:opentelemetry_api, "~> 1.2"},
      {:opentelemetry_cowboy, "~> 0.2"},
      {:opentelemetry_ecto, "~> 1.2"},
      {:opentelemetry_exporter, "~> 1.6"},
      {:opentelemetry_phoenix, "~> 1.1"},
      {:opentelemetry_tesla, "~> 2.2.0"},
      {:params, "~> 2.2"},
      {:phoenix, "~> 1.7"},
      {:phoenix_ecto, "~> 4.4"},
      {:phoenix_live_dashboard, "~> 0.8"},
      {:plug_cowboy, "~> 2.5"},
      {:poison, "~> 6.0", override: true},
      {:postgrex, "~> 0.19"},
      {:proper_case, "~> 1.3"},
      {:recase, "~> 0.8", override: true},
      {:scrivener_ecto, "~> 3.1"},
      {:slugify, "~> 1.3"},
      {:stream_data, "~> 1.1", only: [:dev, :test]},
      # there is a bug in 1.4.2
      {:styler, "1.4.0", only: [:dev, :test], runtime: false},
      {:swoosh, "~> 1.3"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:tesla, "~> 1.4"},
      {:timex, "~> 3.7"},
      {:tzdata, "~> 1.1", override: true},
      # fix unleash to resolve dependencies conflict with telemetry
      {:unleash, "1.9.0", optional: true},
      {:uuid, "~> 1.1"},
      {:versioce, "~> 2.0", override: true}
    ]

    priv_deps =
      if Mix.env() == :dev do
        [
          {:adyen, path: "../ex_adyen_elixir_api_library", override: true},
          {:ex_gcp, path: "../ex_gcp", override: true},
          {:ex_ikarus, path: "../ex_ikarus", override: true},
          {:ex_rbac, path: "../ex_rbac", override: true},
          {:ex_service_client, path: "../ex_service_client", override: true},
          {:seatsio, path: "../ex_seatsio", override: true},
          {:secrets, path: "../ex_secrets", override: true}
        ]
      else
        [
          {:adyen, "~> 0.1", organization: "stagedates", override: true},
          {:ex_gcp, "~> 0.1", organization: "stagedates", override: true},
          {:ex_ikarus, "~> 2.0", organization: "stagedates", override: true},
          {:ex_rbac, "~> 1.6", organization: "stagedates", override: true},
          {:ex_service_client, "~> 1.12", organization: "stagedates", override: true},
          {:seatsio, "~> 0.3", organization: "stagedates", override: true},
          {:secrets, "~> 0.1", organization: "stagedates", override: true}
        ]
      end

    pub_deps ++ priv_deps
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"]
    ]
  end
end
