defmodule EventsServiceWeb.ApiSchemas.EventSchema do
  @moduledoc false
  alias OpenApiSpex.Schema

  defmodule Artist do
    @moduledoc false
    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "An Artist object",
      description: "Artist entity",
      type: :object,
      properties: %{
        id: %Schema{type: :string, description: "Artist id"},
        label: %Schema{type: :string, description: "Artist name"},
        own_id: %Schema{type: :string, format: :uuid, description: "Artist internal id"}
      },
      additionalProperties: false,
      example: %{
        "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "label" => "Artist name",
        "own_id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
      }
    })
  end

  defmodule Event do
    @moduledoc false
    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "An Event object",
      description: "Event entity",
      type: :object,
      properties: %{
        id: %Schema{type: :string, description: "Event id"},
        title: %Schema{type: :string, description: "Event title"},
        subtitle: %Schema{type: :string, description: "Event subtitle"},
        category: %Schema{type: :string, description: "Event category"},
        description: %Schema{type: :string, description: "Event description"},
        artists: %Schema{type: :array, items: Artist, description: "Event artists"},
        admissionDate: %Schema{type: :naive_datetime, description: "Admission date"},
        startDate: %Schema{type: :naive_datetime, description: "Event start date"},
        endDate: %Schema{type: :naive_datetime, description: "Event end date"},
        openEnd: %Schema{type: :boolean, description: "Shows event as open-ended to customers"},
        chartKey: %Schema{type: :string, format: :uuid, description: "Event chart key"},
        venueId: %Schema{type: :string, format: :uuid, description: "Venue id"},
        thumbnailUrl: %Schema{type: :string, description: "Event thumbnail url"},
        coverUrl: %Schema{type: :string, description: "Event cover url"},
        donationRecipientId: %Schema{type: :string, format: :uuid, description: "Donation recipient id"},
        donationAmount: %Schema{type: :integer, description: "Donation amount"},
        ticketColor: %Schema{type: :string, description: "Ticket color"}
      },
      additionalProperties: false,
      example: %{
        "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "title" => "Event title",
        "subtitle" => "Event subtitle",
        "category" => "Event category",
        "description" => "Event description",
        "artists" => [
          %{
            "id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
            "label" => "Artist name",
            "own_id" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935"
          }
        ],
        "admissionDate" => "2021-01-01T00:00:00Z",
        "startDate" => "2021-01-01T00:00:00Z",
        "endDate" => "2021-01-01T20:00:00Z",
        "openEnd" => false,
        "chartKey" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "venueId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "thumbnailUrl" => "https://thumbnail.url",
        "coverUrl" => "https://cover.url",
        "donationRecipientId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
        "donationAmount" => 1000,
        "ticketColor" => "#000000"
      }
    })
  end

  defmodule EventParams do
    @moduledoc false
    alias EventsServiceWeb.ApiSchemas.TicketCategorySchema.TicketCategoryParams

    require OpenApiSpex

    OpenApiSpex.schema(
      # styler:sort
      %{
        description: "POST/PUT body for creating an event",
        example: %{
          "title" => "Event title",
          "subtitle" => "Event subtitle",
          "category" => "Event category",
          "description" => "Event description",
          "artists" => [
            %{
              "id" => "7bXgB6jMjp9ATFy66eO08Z",
              "label" => "Chris Brown"
            }
          ],
          "admissionDate" => "2021-01-01T00:00:00Z",
          "startDate" => "2021-01-01T00:00:00Z",
          "endDate" => "2021-01-01T20:00:00Z",
          "openEnd" => false,
          "chartKey" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
          "venueId" => "9f1ca1e9-53f3-4ee3-9c27-1a8ddf75a935",
          "thumbnailPath" => "https://thumbnail.path",
          "coverPath" => "https://cover.path",
          "visible" => true,
          "showLogoOnBanner" => true,
          "kickback" => 1000,
          "ticketCategories" => [
            %{
              "name" => "VIP",
              "taxRate" => 0.2,
              "description" => "VIP ticket"
            }
          ]
        },
        properties: %{
          title: %Schema{type: :string, description: "Event title"},
          subtitle: %Schema{type: :string, description: "Event subtitle"},
          category: %Schema{type: :string, description: "Event category"},
          description: %Schema{type: :string, description: "Event description"},
          artists: %Schema{type: :array, items: Artist, description: "Event artists"},
          admissionDate: %Schema{type: :naive_datetime, description: "Admission date"},
          startDate: %Schema{type: :naive_datetime, description: "Event start date"},
          endDate: %Schema{type: :naive_datetime, description: "Event end date"},
          openEnd: %Schema{type: :boolean, description: "Shows event as open-ended to customers"},
          chartKey: %Schema{type: :string, format: :uuid, description: "Event chart key"},
          venueId: %Schema{type: :string, format: :uuid, description: "Venue id"},
          thumbnailPath: %Schema{type: :string, description: "Event thumbnail path"},
          coverPath: %Schema{type: :string, description: "Event cover path"},
          visible: %Schema{type: :boolean, description: "Event visibility"},
          showLogoOnBanner: %Schema{type: :boolean, description: "Show logo on banner", deprecated: true},
          kickback: %Schema{type: :integer, description: "Event kickback"},
          ticketCategories: %Schema{
            type: :array,
            items: TicketCategoryParams,
            description: "Event ticket categories",
            deprecated: true
          }
        },
        required: [
          :title,
          :category,
          :description,
          :admissionDate,
          :startDate,
          :venueId,
          :thumbnailPath,
          :coverPath,
          :visible
        ],
        title: "EventParams",
        type: :object
      }
    )
  end
end
