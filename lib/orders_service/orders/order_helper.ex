defmodule OrdersService.Orders.OrderHelper do
  @moduledoc """
  Contains some helper functions.
  """
  alias Ecto.UUID
  alias ExServiceClient.Services.EventsService
  alias ExServiceClient.Services.EventsService.Address, as: <PERSON><PERSON><PERSON>ddress
  alias ExServiceClient.Services.EventsService.SalesChannel
  alias ExServiceClient.Services.EventsService.Sellers.Seller
  alias ExServiceClient.Services.EventsService.Sellers.Store
  alias ExServiceClient.Services.EventsService.TicketCategory
  alias ExServiceClient.Services.EventsService.Variant
  alias OrdersService.InvitationOrder
  alias OrdersService.Location
  alias OrdersService.Locations.Address
  alias OrdersService.Order, as: OrderDB
  alias OrdersService.Orders.Order
  alias OrdersService.Orders.OrderItems.InitialOrderItem

  require Logger

  @order_timeout_minutes 15

  @booking_office_given_name "Booking Office"

  @doc """
  Retrieves the unit price for a specific event and variant.

  ## Params

  - `event`: A map representing the event, including ticket categories.
  - `variant_id`: The ID of the variant.

  ## Returns

  - `{:ok, unitPrice}`: The unit price if found.
  - `{:error, :unit_price_not_found}`: If the unit price is not found.
  """
  # Replaced by unit_price_for_variant/1
  def unit_price_for_event(%{"variants" => variants} = _event, variant_id) do
    case Enum.find(variants, fn %{"variantId" => id} -> id == variant_id end) do
      %{"unitPrice" => unit_price} when is_integer(unit_price) -> {:ok, unit_price / 100}
      _ -> {:error, :unit_price_not_found}
    end
  end

  @doc """
  Retrieves the tax_rate for a specific event and category.

  ## Params

  - `event`: A map representing the event, including ticket categories.
  - `category_id`: The ID of the category.

  ## Returns

  - `{:ok, tax_rate}`: The unit price if found.
  - `{:error, :tax_rate_not_found}`: If the unit price is not found.
  """
  # Replaced by tax_rate_for_variant/1
  def tax_rate_for_event(%{"ticketCategories" => ticket_categories} = _event, category_id) do
    case Enum.find(ticket_categories, fn %{"id" => id} -> id == category_id end) do
      %{"taxRate" => tax_rate} -> {:ok, tax_rate}
      _ -> {:error, :tax_rate_not_found}
    end
  end

  # currentlty the id is a ticketCategoryId and the variantId is a variantId
  def tax_rate_for_event(%{"variants" => variants} = _event, category_id) do
    case Enum.find(variants, fn %{"id" => id} -> id == category_id end) do
      %{"taxRate" => tax_rate} -> {:ok, tax_rate}
      _ -> {:error, :tax_rate_not_found}
    end
  end

  @doc """
  Retrieves the unit price for a specific variant.

  ## Params

  - `variant`: A struct representing the variant, including the unit price.

  ## Returns

  - `{:ok, unit_price}`: The unit price if found.
  - `{:error, :unit_price_not_found}`: If the unit price is not found.
  """
  @spec unit_price_for_variant(Variant.t()) :: {:ok, float()} | {:error, atom()}
  def unit_price_for_variant(%Variant{unitPrice: unit_price}) when not is_nil(unit_price), do: {:ok, unit_price / 100}

  def unit_price_for_variant(_variant), do: {:error, :unit_price_not_found}

  @doc """
  Retrieves the tax rate for a specific variant.

  ## Params

  - `variant`: A struct representing the variant, including the ticket category.

  ## Returns

  - `{:ok, tax_rate}`: The tax rate if found.
  - `{:error, :tax_rate_not_found}`: If the tax rate is not found.
  """
  @spec tax_rate_for_variant(Variant.t()) :: {:ok, float()} | {:error, atom()}
  def tax_rate_for_variant(%Variant{ticketCategory: %TicketCategory{taxRate: tax_rate}}) when not is_nil(tax_rate),
    do: {:ok, tax_rate}

  def tax_rate_for_variant(_variant), do: {:error, :tax_rate_not_found}

  @doc """
  Calculates the amount of tickets that are discountable based on voucher usage and limits.

  ## Params

  - `voucher_uses`: The number of times the voucher has been used.
  - `voucher`: A map representing the voucher, including redemption limits, min items and quota.
    - `limitPerRedemption`: The maximum number of tickets that can be redeemed per order.
    - `minItems`: The minimum number of items required to redeem the voucher.
    - `quota`: The maximum number of times the voucher can be used (one order is one use).
  - `ticket_amount`: The total amount of tickets.

  ## Returns

  The amount of tickets that are discountable.
  """
  @spec discountable_tickets_amount(integer(), map(), integer()) :: integer()
  def discountable_tickets_amount(
        voucher_uses,
        %{"limitPerRedemption" => -1, "minItems" => -1, "quota" => quota} = _voucher,
        ticket_amount
      )
      when voucher_uses < quota or quota == -1,
      do: ticket_amount

  def discountable_tickets_amount(
        voucher_uses,
        %{"limitPerRedemption" => limit_per_redemption, "minItems" => -1, "quota" => quota} = _voucher,
        ticket_amount
      )
      when voucher_uses < quota or quota == -1,
      do: min(ticket_amount, max(0, limit_per_redemption - voucher_uses))

  def discountable_tickets_amount(
        voucher_uses,
        %{"limitPerRedemption" => -1, "minItems" => min_items, "quota" => quota} = _voucher,
        ticket_amount
      )
      when min_items <= ticket_amount and (voucher_uses < quota or quota == -1),
      do: ticket_amount

  def discountable_tickets_amount(
        voucher_uses,
        %{"limitPerRedemption" => limit_per_redemption, "minItems" => min_items, "quota" => quota} = _voucher,
        ticket_amount
      )
      when min_items <= ticket_amount and (voucher_uses < quota or quota == -1),
      do: min(ticket_amount, max(0, limit_per_redemption - voucher_uses))

  def discountable_tickets_amount(_voucher_uses, _voucher, _ticket_amount), do: 0

  @doc """
  Retrieves personal information from an order item.

  ## Params

  - `order_item`: A map representing the order item, including attendee information.

  ## Returns

  - `{:ok, personal_info}`: A tuple with `:ok` and a map containing the personal information.
  - `{:error, :personal_information_missing}`: A tuple with `:error` and the reason for the error.
  """
  def personal_information_from_order_item(
        %{attendee: %{"id" => id, "name" => ""}},
        %{given_name: gn, family_name: fam} = _created_by,
        nil
      ) do
    {:ok, %{id: id, given_name: gn, family_name: fam}}
  end

  def personal_information_from_order_item(
        %{attendee: %{"id" => id, "name" => ""}},
        %{given_name: gn, family_name: fam} = _created_by,
        item_index
      )
      when not is_nil(gn) and not is_nil(fam) do
    {:ok, %{id: id, given_name: gn, family_name: fam <> " ##{item_index}"}}
  end

  def personal_information_from_order_item(%{attendee: %{"id" => id, "name" => name}}, _created_by, _item_index) do
    {:ok, %{id: id, given_name: name}}
  end

  def personal_information_from_order_item(%{attendee: %{"name" => name}}, _created_by, _item_index) when name != "" do
    {:ok, %{given_name: name}}
  end

  def personal_information_from_order_item(%{}, %{given_name: gn, family_name: fam} = _created_by, item_index) do
    {:ok, %{given_name: gn, family_name: fam <> " ##{item_index}"}}
  end

  def personal_information_from_order_item(_order_item, _created_by, _item_index) do
    {:error, :personal_information_missing}
  end

  def has_personal_information?(%{attendee: %{"name" => ""}}), do: false
  def has_personal_information?(%{attendee: %{"name" => " "}}), do: false
  def has_personal_information?(%{attendee: %{"name" => _name}}), do: true
  def has_personal_information?(_order_item), do: false

  @doc """
  Retrieves the seat from an order item.

  ## Params

  - `order_item`: A map representing the order item, including attendee information.

  ## Returns

  - `seat`: The seat if found.
  - `nil`: If the seat is not found.
  """
  def seat_from_order_item(%{attendee: %{"seat" => seat}}), do: seat

  def seat_from_order_item(_order_item), do: nil

  @doc """
  Retrieves personal information from order parameters.

  ## Params

  - `order_params`: A map representing the order parameters, including billing address and user info.

  ## Returns

  - `{:ok, personal_info}`: A tuple with `:ok` and a map containing the personal information.
  - `{:error, :personal_information_missing}`: A tuple with `:error` and the reason for the error.
  """
  def personal_information_from_order_params(%{
        "seller_id" => seller_id,
        "seller_email" => seller_email,
        "user_payload" => user_payload
      })
      when seller_id not in [nil, ""] and seller_email not in [nil, ""] do
    family_name = extract_display_name(user_payload)

    base_info = %{email: seller_email, given_name: @booking_office_given_name, family_name: family_name}
    personal_info_with_address = add_seller_address_to_personal_info(base_info, seller_id)

    {:ok, personal_info_with_address}
  end

  def personal_information_from_order_params(%{"seller_id" => seller_id, "seller_email" => seller_email})
      when seller_id not in [nil, ""] do
    # "bo_sale" means that this order comes from a booking office. this was discussed with @boes and @kap
    base_info = %{email: seller_email, given_name: "bo_sale", family_name: "bo_sale"}
    personal_info_with_address = add_seller_address_to_personal_info(base_info, seller_id)

    {:ok, personal_info_with_address}
  end

  def personal_information_from_order_params(%{
        "personalInformation" => %{
          "gender" => gender,
          "givenName" => given_name,
          "familyName" => family_name,
          "email" => email,
          "countryIso" => country_iso,
          "postalCode" => postal_code,
          "city" => city,
          "birthdate" => birthdate
        },
        "userId" => user_id
      }) do
    user_id_key =
      case UUID.dump(user_id) do
        {:ok, _} -> :user_id
        _ -> :user_document_id
      end

    {:ok,
     %{
       :gender => gender,
       :given_name => given_name,
       :family_name => family_name,
       :email => email,
       :country_iso => country_iso,
       :postal_code => postal_code,
       :city => city,
       :birthdate => birthdate,
       user_id_key => user_id
     }}
  end

  # used for guest list invitations, there are no address information
  def personal_information_from_order_params(%{
        "personalInformation" => %{
          "gender" => gender,
          "givenName" => given_name,
          "familyName" => family_name,
          "email" => email,
          "birthdate" => birthdate
        },
        "userId" => user_id
      }) do
    user_id_key =
      case UUID.dump(user_id) do
        {:ok, _} -> :user_id
        _ -> :user_document_id
      end

    {:ok,
     %{
       :gender => gender,
       :given_name => given_name,
       :family_name => family_name,
       :email => email,
       :country_iso => nil,
       :postal_code => nil,
       :city => nil,
       :birthdate => birthdate,
       user_id_key => user_id
     }}
  end

  def personal_information_from_order_params(_order) do
    {:error, :personal_information_missing}
  end

  @spec add_seller_address_to_personal_info(map(), binary()) :: map()
  defp add_seller_address_to_personal_info(personal_info, seller_id) do
    case get_seller_address(seller_id) do
      {:ok, address_info} -> Map.merge(personal_info, address_info)
      {:error, _reason} -> personal_info
    end
  end

  @spec get_seller_address(binary()) :: {:ok, map()} | {:error, any()}
  defp get_seller_address(seller_id) when seller_id in [nil, ""], do: {:error, :no_seller_id}

  defp get_seller_address(seller_id) do
    with {:ok, %Seller{sellerData: %Store{address: %SellerAddress{} = address}}} <- Seller.get(seller_id),
         {:ok, address_map} <- extract_address_fields(address) do
      {:ok, address_map}
    else
      {:ok, seller} ->
        Logger.warning(
          "Could not get the seller address for seller_id #{seller_id} because seller is missing address: #{inspect(seller)}"
        )

        {:error, :incomplete_address}

      {:error, reason} ->
        Logger.error("Failed to fetch seller address for seller_id: #{seller_id}, reason: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @spec extract_address_fields(SellerAddress.t()) :: {:ok, map()} | {:error, :incomplete_address}
  defp extract_address_fields(%SellerAddress{locality: city, postalCode: postal_code, countryIso: country_iso})
       when city not in [nil, ""] and postal_code not in [nil, ""] and country_iso not in [nil, ""] do
    {:ok, %{city: city, postal_code: postal_code, country_iso: country_iso}}
  end

  defp extract_address_fields(_address), do: {:error, :incomplete_address}

  @doc """
  Map the address parameter (given by a client) to an Address struct that can be stored in our database.
  """
  @spec map_address_from_order_params(address :: map() | nil) :: Address.t() | nil
  def map_address_from_order_params(
        %{"city" => city, "countryIso" => country_iso, "postalCode" => postal_code} = address
      ) do
    country_id =
      case Location.get_country_by_iso(country_iso) do
        nil -> nil
        %{id: country_id} -> country_id
      end

    %Address{
      locality: city,
      country_id: country_id,
      postal_code: postal_code,
      street_address: Map.get(address, "streetAddress"),
      company: Map.get(address, "companyName"),
      vat_id: Map.get(address, "vatId")
    }
  end

  def map_address_from_order_params(_address), do: nil

  @doc """
  Retrieves the start dates from a list of order items and returns
  the earliest start date from the events.

  ## Params

  - `order_items`: A list of order items.

  ## Returns

  - `start_date`: The earliest start date from the events.
  """
  def earliest_start_date_from_order_items(order_items) do
    Enum.reduce(order_items, nil, fn order_item, min_date ->
      date_res = start_date_from_order_item(order_item)

      with {:ok, date} <- date_res,
           {:init, min_date, _date} when not is_nil(min_date) <- {:init, min_date, date} do
        Enum.min([min_date, Timex.to_date(date)], Date)
      else
        {:error, msg} ->
          Logger.error("Error parsing date: #{msg}")
          min_date

        {:init, _min_date, date} ->
          Timex.to_date(date)
      end
    end)
  end

  @doc """
  Gets all pending orders, check the validity periods and set status to TIMEDOUT if necessary.

  ## Returns

  - {:ok, %{count_pending_orders: `count of pending orders`, count_updated: `count of updated orders`, count_failed: `count of failed orders`}}
  """
  def cleanup_pending do
    Logger.info("Cleaning up expired pending orders")

    get_non_invitation_expired_orders()
    |> timeout_orders()
    |> build_cleanup_result()
  end

  @doc """
  Gets voucher by code if it exists and can be redeemed.

  ## Params

  - `voucher_code`: The voucher code.

  ## Returns

  - `{:ok, voucher}`: A tuple with `:ok` and the voucher.
  - `{:ok, nil}`: A tuple with `:ok` and nil in case no voucher code is being used or a not valid code is being used.
  - `{:error,  %{message: %{"message" => message}, status: code}}}`: A tuple with `:error` and the reason for the error.
  """
  def maybe_get_voucher(nil), do: {:ok, nil}

  def maybe_get_voucher(""), do: {:ok, nil}

  def maybe_get_voucher(voucher_code) do
    case EventsService.use_voucher_by_code(voucher_code) do
      {:ok, voucher} -> {:ok, voucher}
      {:error, %{status: 422}} -> {:ok, nil}
      {:error, error} -> {:error, error}
    end
  end

  @doc """
  Helper function to check if one of the order items needs to be a hard ticket.

  ## Params

  - `order_items`: The order items.

  ## Returns

  - `true`: If one of the order items is a hard ticket.
  - `false`: If none of the order items is a hard ticket.
  """
  def hard_tickets_for_order_items?(order_items) do
    Enum.any?(order_items, fn order_item ->
      hard_tickets_for_order_item?(order_item)
    end)
  end

  @doc """
  Helper function to check if an order item needs to be a hard ticket.

  ## Params

  - `order_item`: The order item.

  ## Returns

  - `true`: If the order item is a hard ticket.
  - `false`: If the order item is not a hard ticket.
  """
  def hard_tickets_for_order_item?(%InitialOrderItem{
        hard_ticket: hard_ticket,
        variant: %{ticketCategory: ticket_category}
      }) do
    case {ticket_category, hard_ticket} do
      {%{ticketType: "OPTIONAL"}, true} -> true
      {%{ticketType: "HARD"}, _hard_ticket} -> true
      _ -> false
    end
  end

  def hard_tickets_for_order_item?(_order_item), do: false

  def calculate_hard_tickets_tax_rate_for_order_items(order_items) do
    {amount_of_objects, order_fee_tax_rate_sum} =
      order_items
      |> Enum.filter(&hard_tickets_for_order_item?/1)
      |> Enum.reduce(
        {0, 0.0},
        fn %{amount: amount, variant: %{ticketCategory: %{taxRate: tax_rate}}}, {acc_amount, acc_tax_rate} ->
          {amount + acc_amount, amount * tax_rate + acc_tax_rate}
        end
      )

    case amount_of_objects do
      # Fallback if there is no order item with a tax rate
      0 ->
        Decimal.from_float(0.19)

      _ ->
        Decimal.from_float(order_fee_tax_rate_sum / amount_of_objects)
    end
  end

  @doc """
  Attaches distribution type information to order tickets.

  For tickets with distribution_type :SALES_CHANNEL, fetches the corresponding
  sales channel information and attaches it to the ticket. For other distribution
  types, adds basic type information.

  ## Params

  - `order`: An OrderDB struct with preloaded order_tickets and tickets.

  ## Returns

  - `{:ok, order}`: The order with distribution type information attached to tickets.
  - `{:error, reason}`: If there was an error fetching distribution type information.
  """
  @spec attach_distribution_types_to_order(OrderDB.t()) :: {:ok, OrderDB.t()} | {:error, any()}
  def attach_distribution_types_to_order(%OrderDB{order_tickets: order_tickets} = order) do
    sales_channels_map = get_sales_channels_for_order_tickets(order_tickets)
    updated_order_tickets = attach_sales_channels_to_order_tickets(order_tickets, sales_channels_map)

    {:ok, %{order | order_tickets: updated_order_tickets}}
  rescue
    error ->
      Logger.error("Error attaching distribution types to order #{order.id}: #{inspect(error)}")
      {:error, error}
  end

  @doc """
  Fetches sales channels for order tickets that have SALES_CHANNEL distribution type.

  ## Params

  - `order_tickets`: List of OrderTicket structs with preloaded tickets.

  ## Returns

  - `sales_channels_map`: Map of sales_channel_id -> SalesChannel struct. Missing/failed lookups are omitted and a warning is logged.
  """
  @spec get_sales_channels_for_order_tickets([OrdersService.OrderTicket.t()]) :: map()
  def get_sales_channels_for_order_tickets(order_tickets) do
    sales_channel_ids = extract_sales_channel_ids(order_tickets)

    Enum.reduce(sales_channel_ids, %{}, fn sales_channel_id, acc ->
      case SalesChannel.get(sales_channel_id) do
        {:ok, sales_channel} ->
          Map.put(acc, sales_channel_id, sales_channel)

        {:error, reason} ->
          Logger.warning("Failed to fetch sales channel #{sales_channel_id}: #{inspect(reason)}")
          acc
      end
    end)
  end

  @doc """
  Extracts unique sales channel IDs from order tickets.

  ## Params

  - `order_tickets`: List of OrderTicket structs with preloaded tickets.

  ## Returns

  - List of unique sales channel IDs for tickets with SALES_CHANNEL distribution type.
  """
  @spec extract_sales_channel_ids([OrdersService.OrderTicket.t()]) :: [binary()]
  def extract_sales_channel_ids(order_tickets) do
    order_tickets
    |> Enum.filter(&sales_channel_ticket?/1)
    |> Enum.map(& &1.ticket.distribution_type_id)
    |> Enum.uniq()
  end

  @doc """
  Attaches sales channel information to a list of order tickets.

  ## Params

  - `order_tickets`: List of OrderTicket structs with preloaded tickets.
  - `sales_channels_map`: Map of sales_channel_id -> SalesChannel struct.

  ## Returns

  - List of OrderTicket structs with sales channel information attached to tickets.
  """
  @spec attach_sales_channels_to_order_tickets([OrdersService.OrderTicket.t()], map()) :: [
          OrdersService.OrderTicket.t()
        ]
  def attach_sales_channels_to_order_tickets(order_tickets, sales_channels_map) do
    Enum.map(order_tickets, fn order_ticket ->
      updated_ticket = attach_distribution_type_to_ticket(order_ticket.ticket, sales_channels_map)
      %{order_ticket | ticket: updated_ticket}
    end)
  end

  @doc """
  Attaches distribution type information to a single ticket.

  ## Params

  - `ticket`: A Ticket struct.
  - `sales_channels_map`: Map of sales_channel_id -> SalesChannel struct.

  ## Returns

  - Ticket struct with distribution_type_info field added.
  """
  @spec attach_distribution_type_to_ticket(OrdersService.Ticket.t(), map()) :: OrdersService.Ticket.t()
  def attach_distribution_type_to_ticket(
        %{distribution_type: :SALES_CHANNEL, distribution_type_id: distribution_type_id} = ticket,
        sales_channels_map
      )
      when not is_nil(distribution_type_id) do
    distribution_type_info =
      case Map.get(sales_channels_map, distribution_type_id) do
        nil ->
          Logger.error("Sales channel not found for distribution_type_id: #{distribution_type_id}")
          nil

        sales_channel ->
          sales_channel
      end

    %{ticket | distribution_type_info: distribution_type_info}
  end

  def attach_distribution_type_to_ticket(ticket, _sales_channels_map), do: ticket

  @doc """
  Helper function to check if a category needs to be a hard ticket.

  ## Params

  - `ticket_category`: The ticket category.

  ## Returns

  - `true`: If the order item is a hard ticket.
  - `false`: If the order item is not a hard ticket.
  """
  @spec mandatory_hard_tickets_for_category?(TicketCategory.t()) :: boolean()
  def mandatory_hard_tickets_for_category?(%TicketCategory{ticketType: "HARD"}), do: true
  def mandatory_hard_tickets_for_category?(_ticket_category), do: false

  defp start_date_from_order_item(%{event: %{"startDate" => start_date}}), do: Timex.parse(start_date, "{ISO:Extended}")

  defp start_date_from_order_item(%{event: %{startDate: start_date}}), do: {:ok, start_date}
  defp start_date_from_order_item(_order_item), do: {:error, nil}

  defp get_non_invitation_expired_orders do
    guestlist_order_ids = InvitationOrder.get_all_order_ids()

    [:PENDING, :CREATED]
    |> OrdersService.Order.get_all_with_status()
    |> reject_guestlist_orders(guestlist_order_ids)
    |> filter_expired_orders()
  end

  defp reject_guestlist_orders(orders, guestlist_order_ids) do
    Enum.reject(orders, &(&1.id in guestlist_order_ids))
  end

  defp filter_expired_orders(orders) do
    now = DateTime.utc_now()

    Enum.filter(orders, fn %{submitted_date: submitted_date} = _order ->
      DateTime.diff(now, submitted_date, :minute) > @order_timeout_minutes
    end)
  end

  defp timeout_orders(orders) do
    orders
    |> Enum.map(&process_timeout_order/1)
    |> Enum.split_with(&match?({:ok, _}, &1))
  end

  defp process_timeout_order(%{id: order_id} = order) do
    case Order.set_timed_out_status(order) do
      {:ok, _updated_order} ->
        Logger.debug("Successfully timed out order #{order_id}")
        {:ok, order_id}

      error ->
        Logger.error("Failed to timeout order #{order_id}: #{inspect(error)}")
        {:error, {order_id, error}}
    end
  end

  defp build_cleanup_result({successful, failed}) do
    length_successful = length(successful)
    length_failed = length(failed)
    total_orders = length_successful + length_failed

    {:ok,
     %{
       count_pending_orders: total_orders,
       count_updated: length_successful,
       count_failed: length_failed
     }}
  end

  defp extract_display_name(%{"displayName" => display_name}) when display_name not in [nil, ""], do: display_name
  defp extract_display_name(_), do: "bo_sale"

  defp sales_channel_ticket?(%{ticket: %{distribution_type: :SALES_CHANNEL, distribution_type_id: distribution_type_id}})
       when not is_nil(distribution_type_id),
       do: true

  defp sales_channel_ticket?(_), do: false
end
