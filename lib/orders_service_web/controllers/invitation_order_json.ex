defmodule OrdersServiceWeb.InvitationOrderJSON do
  @moduledoc false

  alias OrdersService.InvitationOrder

  def data(%InvitationOrder{} = invitation_order) do
    # styler:sort
    %{
      guestlistId: invitation_order.guestlist_id,
      id: invitation_order.id,
      invitationId: invitation_order.invitation_id,
      orderId: invitation_order.order_id
    }
  end

  def data(_invitation_order), do: nil
end
