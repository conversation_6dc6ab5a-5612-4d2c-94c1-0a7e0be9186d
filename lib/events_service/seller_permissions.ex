defmodule EventsService.SellerPermissions do
  @moduledoc """
  Context for managing SellerPermissions.

  This module handles the propagation of seller-level permissions to individual events.
  When a user is granted permissions on a seller account, those permissions are
  automatically propagated to all events associated with that seller.
  """
  import Ecto.Query

  alias Ecto.Multi
  alias EventsService.Events.Event
  alias EventsService.Events.EventPermission
  alias EventsService.Repo
  alias EventsService.Seller.Organizer
  alias EventsService.Seller.Seller
  alias EventsService.Seller.SellerPermission
  alias EventsService.Sellers
  alias EventsService.Vendor
  alias EventsService.Vendor.Promoter

  require Logger

  @seller_event_permission_mapping %{
    ADMIN: :EVENT_ADMIN,
    OWNER: :EVENT_ADMIN
  }

  @doc """
  Retrieves a seller permission by its ID.

  ## Parameters
    * `id` - The seller permission ID (binary_id)

  ## Returns
    * `%SellerPermission{}` - The seller permission struct if found
    * `nil` - If no seller permission exists with the given ID

  ## Examples
      iex> SellerPermissions.get("123e4567-e89b-12d3-a456-************")
      %SellerPermission{id: "123e4567-e89b-12d3-a456-************", ...}

      iex> SellerPermissions.get("non-existent-id")
      nil
  """
  @spec get(binary()) :: SellerPermission.t() | nil
  def get(id), do: Repo.get(SellerPermission, id)

  @doc """
  Retrieves a seller permission for a specific user and seller.

  Automatically detects whether the user_id is a UUID (user_id field) or a string
  (user_document_id field) and queries the appropriate field.

  ## Parameters
    * `user_id` - User identifier (UUID string or document ID string)
    * `seller_id` - Seller ID (UUID string)

  ## Returns
    * `%SellerPermission{}` - The seller permission struct if found
    * `nil` - If no seller permission exists for the user-seller combination

  ## Examples
      # Using UUID user_id
      iex> SellerPermissions.get_seller_permission("user-uuid", "seller-uuid")
      %SellerPermission{user_id: "user-uuid", seller_id: "seller-uuid", ...}

      # Using document ID
      iex> SellerPermissions.get_seller_permission("user-doc-123", "seller-uuid")
      %SellerPermission{user_document_id: "user-doc-123", seller_id: "seller-uuid", ...}

      iex> SellerPermissions.get_seller_permission("non-existent", "seller-uuid")
      nil
  """
  @spec get_seller_permission(binary(), Ecto.UUID.t()) :: SellerPermission.t() | nil
  def get_seller_permission(user_id, seller_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    Repo.one(
      from sp in SellerPermission,
        where: sp.seller_id == ^seller_id and field(sp, ^user_field) == ^user_id
    )
  end

  @doc """
  Retrieves a seller permission for a specific user and organizer.

  Automatically detects whether the user_id is a UUID (user_id field) or a string
  (user_document_id field) and queries the appropriate field.

  ## Parameters
    * `user_id` - User identifier (UUID string or document ID string)
    * `organizer_id` - Promoter ID (UUID string)

  ## Returns
    * `%SellerPermission{}` - The seller permission struct if found
    * `nil` - If no seller permission exists for the user-organizer combination
  """
  @spec get_seller_permission_for_organizer(binary(), Ecto.UUID.t()) :: SellerPermission.t() | nil
  def get_seller_permission_for_organizer(user_id, organizer_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    Repo.one(
      from sp in SellerPermission,
        inner_join: s in Seller,
        on: sp.seller_id == s.id and s.type == :ORGANIZER,
        inner_join: o in Organizer,
        on: s.type_id == o.id,
        where: o.promoter_id == ^organizer_id and field(sp, ^user_field) == ^user_id
    )
  end

  @doc """
  Checks if a user has a seller permission for a specific promoter.
  Also checks if the seller_id matches the seller_id in the permission.

  ## Parameters
    * `user_id` - User identifier (UUID string or document ID string)
    * `organizer_id` - Promoter ID (UUID string)
    * `seller_id` - Optional seller ID (UUID string)

  ## Returns
    * `true` if the user has a seller permission for the organizer and no seller_id is provided,
    * `true` if the user has a seller permission for the organizer and the seller_id matches the seller_id in the permission,
    * `false` otherwise
  """
  @spec has_organizer_permission?(binary(), Ecto.UUID.t()) :: boolean()
  @spec has_organizer_permission?(binary(), Ecto.UUID.t(), Ecto.UUID.t() | nil) :: boolean()
  def has_organizer_permission?(user_id, organizer_id, seller_id \\ nil) do
    case {get_seller_permission_for_organizer(user_id, organizer_id), seller_id} do
      {%SellerPermission{}, nil} -> true
      {%SellerPermission{seller_id: seller_id}, seller_id} -> true
      _ -> false
    end
  end

  @doc """
  Lists seller permissions with optional filtering.

  ## Parameters
    * `filters` - Map containing optional filter criteria:
      * `:seller_id` - Filter by seller ID
      * `:user_id` - Filter by user ID
      * `:user_document_id` - Filter by user document ID

  ## Returns
    * List of `%SellerPermission{}` structs matching the criteria

  ## Examples
      iex> SellerPermissions.list(%{seller_id: "123e4567-e89b-12d3-a456-************"})
      [%SellerPermission{seller_id: "123e4567-e89b-12d3-a456-************", ...}]

      iex> SellerPermissions.list(%{})
      [%SellerPermission{}, %SellerPermission{}, ...]
  """
  @spec list(map()) :: [SellerPermission.t()]
  def list(filters \\ %{}) do
    query = from(sp in SellerPermission)

    query
    |> maybe_filter_by_seller_id(filters)
    |> maybe_filter_by_user_id(filters)
    |> maybe_filter_by_user_document_id(filters)
    |> Repo.all()
  end

  @doc """
  Paginates seller permissions with optional filtering.

  ## Parameters
    * `params` - Map containing filter criteria and pagination options:
      * `:seller_id` - Filter by seller ID
      * `:user_id` - Filter by user ID
      * `:user_document_id` - Filter by user document ID
      * `:page` - Page number (defaults to 1)
      * `:page_size` - Number of items per page (defaults to 10)

  ## Returns
    * `%Scrivener.Page{}` containing paginated results

  ## Examples
      iex> SellerPermissions.paginate(%{seller_id: "123", page: 1, page_size: 20})
      %Scrivener.Page{entries: [%SellerPermission{}, ...], page_number: 1, ...}
  """
  @spec paginate(map()) :: Scrivener.Page.t()
  def paginate(params \\ %{}) do
    query = from(sp in SellerPermission)

    query
    |> maybe_filter_by_seller_id(params)
    |> maybe_filter_by_user_id(params)
    |> maybe_filter_by_user_document_id(params)
    |> Repo.paginate(params)
  end

  @doc """
  Creates a new seller permission and propagates it to existing events.

  This function creates a seller permission and automatically propagates the permissions
  to all existing events associated with the seller. The operation is performed within
  a database transaction to ensure consistency.

  ## Parameters
    * `attrs` - Map containing seller permission attributes:
      * `:seller_id` (required) - Seller ID (UUID string)
      * `:roles` (required) - List of roles (`:ADMIN`, `:OWNER`)
      * `:user_id` (optional) - UUID user identifier
      * `:user_document_id` (optional) - Document user identifier

  Note: Either `:user_id` or `:user_document_id` must be provided.

  ## Returns
    * `{:ok, %SellerPermission{}}` - On successful creation
    * `{:error, %Ecto.Changeset{}}` - On validation errors
    * `{:error, reason}` - On other errors (e.g., database constraints)

  ## Examples
      iex> attrs = %{seller_id: "seller-uuid", user_id: "user-uuid", roles: [:ADMIN]}
      iex> SellerPermissions.create(attrs)
      {:ok, %SellerPermission{seller_id: "seller-uuid", user_id: "user-uuid", roles: [:ADMIN]}}

      iex> invalid_attrs = %{seller_id: nil, roles: []}
      iex> SellerPermissions.create(invalid_attrs)
      {:error, %Ecto.Changeset{errors: [seller_id: {"can't be blank", [validation: :required]}]}}
  """
  @spec create(map()) :: {:ok, SellerPermission.t()} | {:error, Ecto.Changeset.t()} | {:error, term()}
  def create(attrs) do
    Multi.new()
    |> Multi.insert(:seller_permission, SellerPermission.changeset(%SellerPermission{}, attrs))
    |> maybe_propagate_event_permissions()
    |> Repo.transaction()
    |> case do
      {:ok, %{seller_permission: sp}} -> {:ok, sp}
      {:error, :seller_permission, changeset, _} -> {:error, changeset}
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  @doc """
  Updates an existing seller permission and propagates changes to associated events.

  This function updates a seller permission and automatically propagates any new roles
  to all existing events associated with the seller. Only new roles (not previously
  assigned) are propagated to avoid unnecessary updates.

  ## Parameters
    * `seller_permission` - The existing `%SellerPermission{}` struct to update
    * `attrs` - Map containing updated attributes (same format as `create/1`)

  ## Returns
    * `{:ok, %SellerPermission{}}` - On successful update
    * `{:error, %Ecto.Changeset{}}` - On validation errors
    * `{:error, reason}` - On other errors (e.g., database constraints)

  ## Examples
      iex> permission = SellerPermissions.get(permission_id)
      iex> SellerPermissions.update(permission, %{roles: [:ADMIN, :OWNER]})
      {:ok, %SellerPermission{roles: [:ADMIN, :OWNER]}}

      iex> SellerPermissions.update(permission, %{seller_id: nil})
      {:error, %Ecto.Changeset{errors: [seller_id: {"can't be blank", [validation: :required]}]}}
  """
  @spec update(SellerPermission.t(), map()) ::
          {:ok, SellerPermission.t()} | {:error, Ecto.Changeset.t()} | {:error, term()}
  def update(%SellerPermission{roles: old_roles} = seller_permission, attrs) do
    Multi.new()
    |> Multi.update(:seller_permission, SellerPermission.changeset(seller_permission, attrs))
    |> maybe_propagate_event_permissions(old_roles)
    |> Repo.transaction()
    |> case do
      {:ok, %{seller_permission: sp}} -> {:ok, sp}
      {:error, :seller_permission, changeset, _} -> {:error, changeset}
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  @doc """
  Soft deletes a seller permission and removes associated event permissions.

  This function performs a soft delete on the seller permission (sets `deleted_at`)
  and removes all associated event permissions by soft deleting them as well.
  The operation is performed within a database transaction.

  ## Parameters
    * `seller_permission` - The `%SellerPermission{}` struct to delete

  ## Returns
    * `{:ok, %SellerPermission{}}` - On successful deletion (with `deleted_at` set)
    * `{:error, %Ecto.Changeset{}}` - On validation errors
    * `{:error, reason}` - On other errors

  ## Examples
      iex> permission = SellerPermissions.get(permission_id)
      iex> SellerPermissions.delete(permission)
      {:ok, %SellerPermission{deleted_at: ~U[2024-01-01 12:00:00Z]}}
  """
  @spec delete(SellerPermission.t()) :: {:ok, SellerPermission.t()} | {:error, Ecto.Changeset.t()} | {:error, term()}
  def delete(%SellerPermission{} = seller_permission) do
    Multi.new()
    |> Multi.update(
      :seller_permission,
      SellerPermission.changeset(seller_permission, %{deleted_at: DateTime.utc_now()})
    )
    |> remove_event_permissions(seller_permission)
    |> Repo.transaction()
    |> case do
      {:ok, %{seller_permission: sp}} -> {:ok, sp}
      {:error, :seller_permission, changeset, _} -> {:error, changeset}
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  @doc """
  Propagates existing seller permissions to a newly created event.

  This function takes a newly created event and applies all existing seller permissions
  from the event's associated seller (via promoter) to the event. This ensures that
  users with seller-level permissions automatically get access to new events.

  ## Parameters
    * `event` - The `%Event{}` struct for the newly created event

  ## Returns
    * `:ok` - On successful propagation or when no seller permissions exist
    * `{:error, reason}` - On propagation failure

  ## Examples
      iex> event = %Event{id: "event-uuid", promoter_id: "promoter-uuid"}
      iex> SellerPermissions.propagate_permissions_to_new_event(event)
      :ok

      # When propagation fails
      iex> SellerPermissions.propagate_permissions_to_new_event(invalid_event)
      {:error, :some_reason}
  """
  @spec propagate_permissions_to_new_event(Event.t()) :: :ok | {:error, term()}
  def propagate_permissions_to_new_event(event) do
    Multi.new()
    |> add_seller_permissions_to_event(event)
    |> Repo.transaction()
    |> case do
      {:ok, _} -> :ok
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  @doc """
  Adds seller permission propagation to an existing Ecto.Multi transaction.

  This function adds a step to an existing `Ecto.Multi` that will propagate seller
  permissions to an event. It can work with either an event struct directly or
  an atom key that references an event created earlier in the multi.

  ## Parameters
    * `multi` - The `Ecto.Multi` struct to add the propagation step to
    * `event` - Either:
      * An `%Event{}` struct - propagates permissions to this specific event
      * An atom - references an event created earlier in the multi with this key

  ## Returns
    * `Ecto.Multi` - The updated multi with the propagation step added

  ## Examples
      # Using with event struct
      iex> multi = Ecto.Multi.new()
      iex> event = %Event{id: "event-uuid", promoter_id: "promoter-uuid"}
      iex> SellerPermissions.add_seller_permissions_to_event(multi, event)
      %Ecto.Multi{...}

      # Using with event key from multi
      iex> multi = Ecto.Multi.new()
      iex> |> Ecto.Multi.insert(:event, event_changeset)
      iex> |> SellerPermissions.add_seller_permissions_to_event(:event)
      %Ecto.Multi{...}
  """
  @spec add_seller_permissions_to_event(Multi.t(), Event.t() | atom()) :: Multi.t()
  def add_seller_permissions_to_event(multi, event) when is_struct(event) do
    Multi.run(multi, :propagate_seller_permissions, fn repo, _ ->
      do_propagate_permissions_to_event(repo, event)
    end)
  end

  def add_seller_permissions_to_event(multi, event_key) when is_atom(event_key) do
    Multi.run(multi, :propagate_seller_permissions, fn repo, %{^event_key => event} ->
      do_propagate_permissions_to_event(repo, event)
    end)
  end

  defp do_propagate_permissions_to_event(repo, %Event{id: event_id, promoter_id: promoter_id}) do
    case Vendor.get_promoter(promoter_id, organizer: [seller: [:permissions]]) do
      %Promoter{organizer: %Organizer{seller: %Seller{permissions: permissions}}} when permissions != [] ->
        permissions_to_create = build_event_permissions_from_seller_permissions(permissions, event_id)

        insert_event_permissions(repo, permissions_to_create)

      _ ->
        {:ok, :no_seller_permissions}
    end
  end

  defp build_event_permissions_from_seller_permissions(seller_permissions, event_id) do
    seller_permissions
    |> Enum.filter(&Enum.any?(&1.roles))
    |> Enum.map(&build_event_permissions_for_user(&1, event_id))
    |> Enum.uniq_by(&{&1.user_id, &1.user_document_id, &1.event_id})
  end

  defp build_event_permissions_for_user(seller_permission, event_id) do
    %{
      event_id: event_id,
      user_document_id: seller_permission.user_document_id,
      user_id: seller_permission.user_id,
      role: event_permission_roles_from_seller_permission(seller_permission),
      is_inherited_from_seller: true
    }
  end

  defp insert_event_permissions(repo, permissions_to_create) do
    now = DateTime.utc_now() |> DateTime.to_naive() |> NaiveDateTime.truncate(:second)

    permissions_with_timestamps =
      Enum.map(permissions_to_create, fn permission ->
        permission
        |> Map.put(:inserted_at, now)
        |> Map.put(:updated_at, now)
      end)

    EventPermission
    |> repo.insert_all(permissions_with_timestamps, on_conflict: :nothing)
    |> case do
      {_, nil} -> {:ok, permissions_with_timestamps}
      _ -> {:error, :failed_to_insert_permissions}
    end
  end

  defp maybe_propagate_event_permissions(multi, old_roles \\ []) do
    Multi.run(multi, :add_event_permissions, fn repo, %{seller_permission: seller_permission} ->
      %SellerPermission{roles: new_roles} = seller_permission

      if has_new_roles?(old_roles, new_roles) do
        propagate_permissions_to_existing_events(repo, seller_permission)
      else
        {:ok, :no_changes}
      end
    end)
  end

  defp propagate_permissions_to_existing_events(repo, %{seller_id: seller_id} = seller_permission) do
    event_ids = Sellers.get_event_ids_for_seller(seller_id)
    existing_permissions = get_existing_event_permissions_for_seller_permission(repo, seller_permission, event_ids)

    Multi.new()
    |> update_existing_permissions(existing_permissions, seller_permission)
    |> create_missing_permissions(event_ids, existing_permissions, seller_permission)
    |> repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  defp get_existing_event_permissions_for_seller_permission(repo, seller_permission, event_ids) do
    {user_field, user_value} = get_user_identifier(seller_permission)

    repo.all(
      from(ep in EventPermission,
        where: field(ep, ^user_field) == ^user_value,
        where: ep.event_id in ^event_ids,
        where: ep.is_inherited_from_seller == true,
        where: is_nil(ep.deleted_at)
      )
    )
  end

  defp update_existing_permissions(multi, existing_permissions, seller_permission) do
    new_roles = event_permission_roles_from_seller_permission(seller_permission)

    existing_permissions
    |> Enum.with_index()
    |> Enum.reduce(multi, fn {%EventPermission{role: existing_roles} = permission, index}, multi ->
      missing_roles = new_roles -- existing_roles

      if Enum.any?(missing_roles) do
        updated_roles = Enum.uniq(existing_roles ++ missing_roles)
        changeset = EventPermission.changeset(permission, %{role: updated_roles})

        Multi.update(multi, "update_event_permission_#{index}", changeset)
      else
        multi
      end
    end)
  end

  defp create_missing_permissions(multi, event_ids, existing_permissions, seller_permission) do
    roles = event_permission_roles_from_seller_permission(seller_permission)
    missing_event_ids = event_ids -- Enum.map(existing_permissions, & &1.event_id)

    missing_event_ids
    |> Enum.with_index()
    |> Enum.reduce(multi, fn {event_id, index}, acc_multi ->
      permission_attrs = %{
        event_id: event_id,
        user_id: seller_permission.user_id,
        user_document_id: seller_permission.user_document_id,
        role: roles,
        is_inherited_from_seller: true
      }

      changeset = EventPermission.changeset(%EventPermission{}, permission_attrs)

      Multi.insert(acc_multi, "create_event_permission_#{index}", changeset, on_conflict: :nothing)
    end)
  end

  defp remove_event_permissions(multi, seller_permission) do
    Multi.run(multi, :remove_event_permissions, fn repo, _ ->
      event_ids = Sellers.get_event_ids_for_seller(seller_permission.seller_id)
      existing_permissions = get_existing_event_permissions_for_seller_permission(repo, seller_permission, event_ids)

      if Enum.any?(existing_permissions) do
        soft_delete_event_permissions(repo, existing_permissions)
      else
        {:ok, :no_permissions_to_remove}
      end
    end)
  end

  defp soft_delete_event_permissions(repo, event_permissions) do
    now = DateTime.utc_now()

    event_permissions
    |> Enum.with_index()
    |> Enum.reduce(Multi.new(), fn {permission, index}, multi ->
      changeset = EventPermission.changeset(permission, %{deleted_at: now})
      Multi.update(multi, "soft_delete_event_permission_#{index}", changeset)
    end)
    |> repo.transaction()
    |> case do
      {:ok, result} -> {:ok, result}
      {:error, _, reason, _} -> {:error, reason}
    end
  end

  defp event_permission_roles_from_seller_permission(%{roles: roles}) do
    roles
    |> Enum.map(&@seller_event_permission_mapping[&1])
    |> Enum.reject(&is_nil/1)
  end

  defp uuid?(str) when is_binary(str) do
    case Ecto.UUID.dump(str) do
      {:ok, _} -> true
      :error -> false
    end
  end

  defp uuid?(_), do: false

  defp has_new_roles?(old_roles, new_roles) do
    new_roles -- old_roles != []
  end

  defp get_user_identifier(%{user_id: user_id, user_document_id: user_document_id} = _seller_permission) do
    if user_id, do: {:user_id, user_id}, else: {:user_document_id, user_document_id}
  end

  defp maybe_filter_by_seller_id(query, %{seller_id: seller_id}) when is_binary(seller_id) do
    where(query, [sp], sp.seller_id == ^seller_id)
  end

  defp maybe_filter_by_seller_id(query, _), do: query

  defp maybe_filter_by_user_id(query, %{user_id: user_id}) when is_binary(user_id) do
    where(query, [sp], sp.user_id == ^user_id)
  end

  defp maybe_filter_by_user_id(query, _), do: query

  defp maybe_filter_by_user_document_id(query, %{user_document_id: user_document_id})
       when is_binary(user_document_id) do
    where(query, [sp], sp.user_document_id == ^user_document_id)
  end

  defp maybe_filter_by_user_document_id(query, _), do: query
end
