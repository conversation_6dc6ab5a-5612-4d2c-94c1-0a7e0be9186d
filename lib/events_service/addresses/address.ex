defmodule EventsService.Addresses.Address do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  alias EventsService.Addresses.Country
  alias EventsService.Location.GeoCoordinate
  alias EventsService.Location.Venue

  @type t() :: %__MODULE__{
          id: Ecto.UUID.t(),
          country_iso: String.t(),
          street_address: String.t(),
          postal_code: String.t(),
          locality: String.t(),
          region: String.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t(),
          deleted_at: DateTime.t() | nil
        }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "addresses" do
    field :deleted_at, :utc_datetime
    field :locality, :string
    field :postal_code, :string
    field :region, :string
    field :street_address, :string

    belongs_to :country, Country, references: :iso, foreign_key: :country_iso, type: :string

    has_many :venues, Venue
    has_one :geo_coordinate, GeoCoordinate

    timestamps()
  end

  def changeset(address, attrs) do
    address
    |> cast(attrs, [:street_address, :postal_code, :locality, :region, :country_iso, :deleted_at])
    |> validate_required([
      :street_address,
      :postal_code,
      :locality,
      :country_iso
    ])
  end

  def delete_changeset(address, attrs) do
    address
    |> cast(attrs, [:deleted_at])
    |> validate_required([:deleted_at])
  end
end
