defmodule EventsService.Sellers do
  @moduledoc """
  The Sellers context.
  """

  import Ecto.Query, warn: false

  alias Ecto.Multi
  alias EventsService.Addresses.Address
  alias EventsService.Events.Event
  alias EventsService.RBACClient
  alias EventsService.Repo
  alias EventsService.Seller.Organizer
  alias EventsService.Seller.SalesPermission
  alias EventsService.Seller.Seller
  alias EventsService.Seller.SellerPermission
  alias EventsService.Seller.Store
  alias EventsService.Vendor.Promoter
  alias ExRBAC.API.Role
  alias ExServiceClient.Services.AccountsService

  require Logger

  @supported_seller_types [:STORE, :ORGANIZER]

  # styler:sort
  @type seller_params :: %{
          owner_id: Ecto.UUID.t() | nil,
          owner_document_id: String.t() | nil,
          parent_id: String.t() | nil,
          seller_data: map() | nil,
          type: atom() | String.t()
        }

  @doc """
  Creates a seller based on the params structure from SellerCreate.

  ## Examples

      iex> create_seller(%{type: "STORE", owner_id: "owner-uuid", seller_data: %{address: %{street_address: "123 Main St", locality: "City", postal_code: "12345", country_iso: "US"}}})
      {:ok, %Seller{}}

      iex> create_seller(%{type: "STORE", owner_id: nil})
      {:error, :seller, %Ecto.Changeset{}, %{}}
  """
  @spec create_seller(seller_params()) :: {:ok, Seller.t()} | {:error, atom(), Ecto.Changeset.t() | any()}
  def create_seller(%{type: type} = params) when type in @supported_seller_types do
    Multi.new()
    |> insert_type_specific_data(type, params[:seller_data] || %{})
    |> Multi.insert(:seller, fn changes ->
      seller_params = %{
        type: type,
        type_id: get_type_id(changes),
        owner_id: params[:owner_id],
        parent_id: params[:parent_id]
      }

      Seller.changeset(%Seller{}, seller_params)
    end)
    |> Multi.insert(:seller_permission, fn %{seller: seller} ->
      SellerPermission.changeset(%SellerPermission{}, %{
        seller_id: seller.id,
        roles: [:OWNER],
        user_id: seller.owner_id,
        user_document_id: params[:owner_document_id]
      })
    end)
    |> Repo.transaction()
    |> case do
      {:ok, result} ->
        {:ok, build_preloaded_seller(result)}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_operation, failed_value}
    end
  end

  def create_seller(_params) do
    changeset =
      %Seller{}
      |> Seller.changeset(%{})
      |> Ecto.Changeset.add_error(:type, "allowed values: #{Enum.join(@supported_seller_types, ", ")}")

    {:error, :seller, changeset, %{}}
  end

  @doc """
  Creates a seller of type ORGANIZER for a promoter.

  ## Examples

      iex> create_organizer_seller(%Promoter{})
      {:ok, %Seller{}}
  """
  @spec create_organizer_seller(promoter :: Promoter.t()) ::
          {:ok, Seller.t()} | {:error, atom(), Ecto.Changeset.t() | any()} | {:error, :user_not_found}
  def create_organizer_seller(
        %Promoter{id: promoter_id, created_by_document_id: created_by_document_id, created_by: created_by},
        params \\ %{}
      ) do
    case AccountsService.get_user_by_id(created_by || created_by_document_id) do
      {:ok, %{"id" => user_id}} ->
        create_seller(%{
          type: :ORGANIZER,
          owner_id: user_id,
          owner_document_id: created_by_document_id,
          seller_data: Map.put(params, :promoter_id, promoter_id)
        })

      {:error, error} ->
        Logger.error("Failed to get user ID from promoter with id #{promoter_id} because of #{inspect(error)}")
        {:error, :user_not_found}
    end
  end

  @spec get_seller(Ecto.UUID.t(), atom()) :: Seller.t() | nil
  def get_seller(seller_id, preloads) when is_list(preloads) do
    query =
      from s in Seller,
        where: s.id == ^seller_id,
        left_join: store in Store,
        on: s.type == :STORE and store.id == s.type_id,
        left_join: organizer in Organizer,
        on: s.type == :ORGANIZER and organizer.id == s.type_id,
        preload: ^preloads

    query
    |> Repo.one()
    |> maybe_attach_seller_data()
  end

  def get_seller(seller_id, nil), do: get_seller(seller_id)

  @doc """
  Gets all sellers for a specific user.

  ## Examples

      iex> get_sellers_for_user("user-uuid")
      [%Seller{}, ...]

      iex> get_sellers_for_user("nonexistent-user")
      []
  """
  @spec get_sellers_for_user(String.t()) :: [Seller.t()]
  def get_sellers_for_user(user_id) do
    user_id
    |> seller_base_query()
    |> Repo.all()
    |> Enum.map(&maybe_attach_seller_data/1)
  end

  @doc """
  Gets paginated sellers for a specific user.

  ## Examples

      iex> paginate_sellers_for_user("user-uuid", %{page: 1, page_size: 10})
      %Scrivener.Page{entries: [%Seller{}, ...], page_number: 1, ...}
  """
  @spec paginate_sellers_for_user(String.t(), map()) :: Scrivener.Page.t()
  def paginate_sellers_for_user(user_id, params \\ %{}) do
    user_id
    |> seller_base_query()
    |> Repo.paginate(params)
    |> Map.update!(:entries, fn entries ->
      Enum.map(entries, &maybe_attach_seller_data/1)
    end)
  end

  @spec check_permission(%{seller_id: Ecto.UUID.t(), event_ids: [Ecto.UUID.t()], scope: String.t()}) ::
          {:ok, boolean()} | {:error, atom()}
  def check_permission(%{seller_id: seller_id, event_ids: event_ids, scope: "events"}) do
    query =
      from p in SalesPermission,
        where:
          p.seller_id == ^seller_id and
            p.type == :EVENT and
            p.type_id in ^event_ids,
        select: p.type_id

    permitted_event_ids = query |> Repo.all() |> MapSet.new()
    requested_event_ids = MapSet.new(event_ids)
    has_permission = MapSet.subset?(requested_event_ids, permitted_event_ids)
    {:ok, has_permission}
  end

  def check_permission(%{scope: _scope}), do: {:error, :scope_not_supported}

  @doc """
  Creates a sales permission for a seller to access a specific resource (event, organizer, sales channel).
  Also creates the corresponding permission in Casdoor RBAC system.

  For ORGANIZER type permissions, this function also creates individual EVENT permissions
  for all events belonging to that organizer.

  ## Parameters

      - params: Map containing the sales permission parameters
      - type: The type of resource (:ORGANIZER, :EVENT, :SALES_CHANNEL)
      - type_id: The ID of the resource
      - seller_id: The ID of the seller receiving permission

  ## Examples

      iex> create_sales_permission(%{type: :EVENT, type_id: "event-uuid", seller_id: "seller-uuid"})
      {:ok, %{sales_permission: %SalesPermission{}, rbac_permission: String.t()}}

      iex> create_sales_permission(%{type: :ORGANIZER, type_id: "promoter-uuid", seller_id: "seller-uuid"})
      {:ok, %{sales_permission: %SalesPermission{}, rbac_permission: String.t(), event_permissions: [%SalesPermission{}, ...]}}

      iex> create_sales_permission(%{type: :EVENT, type_id: nil, seller_id: "seller-uuid"})
      {:error, :sales_permission, %Ecto.Changeset{}}
  """
  @spec create_sales_permission(map()) ::
          {:ok, %{sales_permission: SalesPermission.t(), rbac_permission: String.t()}}
          | {:error, atom(), any()}
          | {:error, atom(), any(), map()}
  def create_sales_permission(%{type: :ORGANIZER} = params) do
    role = params[:role] || "seller_viewer"

    Multi.new()
    |> Multi.insert(:sales_permission, SalesPermission.changeset(%SalesPermission{}, params))
    |> Multi.run(:rbac_permission, fn _repo, %{sales_permission: sales_permission} ->
      create_rbac_permission(sales_permission, role)
    end)
    |> add_event_permissions(params)
    |> Repo.transaction()
  end

  def create_sales_permission(params) do
    role = params[:role] || "seller_viewer"

    Multi.new()
    |> Multi.insert(:sales_permission, SalesPermission.changeset(%SalesPermission{}, params))
    |> Multi.run(:rbac_permission, fn _repo, %{sales_permission: sales_permission} ->
      create_rbac_permission(sales_permission, role)
    end)
    |> Repo.transaction()
  end

  @doc """
  Gets a sales permission by ID.

  ## Examples

      iex> get_sales_permission("123")
      %SalesPermission{}

      iex> get_sales_permission("456")
      nil
  """
  @spec get_sales_permission(String.t()) :: SalesPermission.t() | nil
  def get_sales_permission(id), do: Repo.get(SalesPermission, id)

  @doc """
  Gets sales permissions with optional filtering.

  ## Parameters

      - filters: Map of optional filters
      - seller_id: (optional) Filter by seller ID
      - type: (optional) Filter by resource type (:ORGANIZER, :EVENT, :SALES_CHANNEL)
      - type_id: (optional) Filter by resource ID

  ## Examples

      iex> get_sales_permissions(%{seller_id: "seller-uuid"})
      [%SalesPermission{}, ...]

      iex> get_sales_permissions(%{type: :EVENT, type_id: "event-uuid"})
      [%SalesPermission{}, ...]

      iex> get_sales_permissions(%{})
      [%SalesPermission{}, ...] # Returns all sales permissions
  """
  @spec get_sales_permissions(map()) :: [SalesPermission.t()]
  def get_sales_permissions(filters \\ %{}) do
    query = from(sp in SalesPermission)

    query
    |> maybe_apply_seller_filter(filters)
    |> maybe_apply_type_filter(filters)
    |> maybe_apply_type_id_filter(filters)
    |> Repo.all()
  end

  @doc """
  Gets paginated sales permissions with optional filtering.

  ## Parameters

      - params: Map of optional filters and pagination options (page, page_size)

  ## Examples

      iex> paginate_sales_permissions(%{seller_id: "seller-uuid", page: 1, page_size: 20})
      %Scrivener.Page{entries: [%SalesPermission{}, ...], page_number: 1, ...}
  """
  @spec paginate_sales_permissions(map()) :: Scrivener.Page.t()
  def paginate_sales_permissions(params) do
    query = from(sp in SalesPermission)

    query
    |> maybe_apply_seller_filter(params)
    |> maybe_apply_type_filter(params)
    |> maybe_apply_type_id_filter(params)
    |> Repo.paginate(params)
  end

  @doc """
  Gets all sales permissions for a particular seller.

  ## Examples

      iex> get_sales_permissions_by_seller("seller-uuid")
      [%SalesPermission{}, ...]
  """
  @spec get_sales_permissions_by_seller(String.t()) :: [SalesPermission.t()]
  def get_sales_permissions_by_seller(seller_id) do
    get_sales_permissions(%{seller_id: seller_id})
  end

  @doc """
  Gets all sales permissions for a specific resource type and ID.

  ## Examples

      iex> get_sales_permissions_by_resource(:EVENT, "event-uuid")
      [%SalesPermission{}, ...]
  """
  @spec get_sales_permissions_by_resource(atom(), String.t()) :: [SalesPermission.t()]
  def get_sales_permissions_by_resource(type, type_id) do
    get_sales_permissions(%{type: type, type_id: type_id})
  end

  @doc """
  Gets all seller IDs that have promoter (ORGANIZER) permissions for a specific promoter.

  ## Examples

      iex> get_seller_ids_with_promoter_permission("promoter-uuid")
      ["seller-uuid-1", "seller-uuid-2"]

      iex> get_seller_ids_with_promoter_permission("nonexistent-promoter")
      []
  """
  @spec get_seller_ids_with_promoter_permission(String.t()) :: [String.t()]
  def get_seller_ids_with_promoter_permission(promoter_id) do
    Repo.all(
      from(sp in SalesPermission,
        where: sp.type == :ORGANIZER and sp.type_id == ^promoter_id,
        select: sp.seller_id
      )
    )
  end

  @spec delete_sales_permission(sales_permission :: SalesPermission.t()) ::
          {:ok, SalesPermission.t()} | {:error, Ecto.Changeset.t()}
  def delete_sales_permission(%SalesPermission{} = sales_permission) do
    sales_permission
    |> SalesPermission.changeset(%{deleted_at: DateTime.utc_now()})
    |> Repo.update()
  end

  @spec get_event_ids_for_seller(seller_id :: Ecto.UUID.t()) :: [Ecto.UUID.t()]
  def get_event_ids_for_seller(seller_id) do
    Repo.all(
      from(e in Event,
        inner_join: p in Promoter,
        on: e.promoter_id == p.id,
        inner_join: o in Organizer,
        on: p.id == o.promoter_id,
        inner_join: s in Seller,
        on: o.id == s.type_id,
        where: s.id == ^seller_id,
        select: e.id
      )
    )
  end

  defp get_type_id(%{store: %{id: store_id}} = _changes), do: store_id
  defp get_type_id(%{organizer: %{id: organizer_id}} = _changes), do: organizer_id

  defp insert_type_specific_data(multi, :STORE, %{address: address_params} = params) do
    multi
    |> Multi.insert(:address, Address.changeset(%Address{}, address_params))
    |> Multi.insert(:store, fn %{address: address} ->
      Store.changeset(%Store{}, %{address_id: address.id, name: params[:name]})
    end)
  end

  defp insert_type_specific_data(multi, :ORGANIZER, params) do
    Multi.insert(multi, :organizer, Organizer.changeset(%Organizer{}, params))
  end

  defp insert_type_specific_data(multi, _type, _data),
    do: Multi.error(multi, :seller_type, "should be STORE or ORGANIZER")

  defp build_preloaded_seller(%{seller: %{type: :STORE} = seller, store: store, address: address}) do
    store_with_address = %{store | address: address}
    %{seller | seller_data: store_with_address}
  end

  defp build_preloaded_seller(%{seller: %{type: :ORGANIZER} = seller, organizer: organizer}) do
    %{seller | seller_data: organizer}
  end

  defp build_preloaded_seller(%{seller: seller}), do: seller

  defp create_rbac_permission(%SalesPermission{seller_id: seller_id}, role) do
    client_config = RBACClient.client_config()
    role_id = "#{RBACClient.organization_name()}/#{role}"

    with {:ok, seller} <- get_seller(seller_id),
         {:ok, user_id} <- get_user_id_from_seller(seller) do
      Role.add_user(client_config, role_id, user_id)
    end
  end

  defp get_seller(seller_id) do
    case Repo.get(Seller, seller_id) do
      nil ->
        Logger.error("Failed to get seller with ID #{seller_id}")
        {:error, :seller_not_found}

      seller ->
        {:ok, seller}
    end
  end

  defp get_user_id_from_seller(%Seller{owner_id: owner_id}) do
    case AccountsService.get_user_by_id(owner_id) do
      {:ok, %{"sub" => external_id}} ->
        {:ok, external_id}

      {:error, error} ->
        Logger.error("Failed to get external user ID from seller #{owner_id} because of #{inspect(error)}")
        {:error, :user_not_found}
    end
  end

  defp maybe_apply_seller_filter(query, %{seller_id: seller_id}) when is_binary(seller_id) do
    where(query, [sp], sp.seller_id == ^seller_id)
  end

  defp maybe_apply_seller_filter(query, _), do: query

  defp maybe_apply_type_filter(query, %{type: type}) when not is_nil(type) do
    where(query, [sp], sp.type == ^type)
  end

  defp maybe_apply_type_filter(query, _), do: query

  defp maybe_apply_type_id_filter(query, %{type_id: type_id}) when is_binary(type_id) do
    where(query, [sp], sp.type_id == ^type_id)
  end

  defp maybe_apply_type_id_filter(query, _), do: query

  defp maybe_attach_seller_data(nil), do: nil

  defp maybe_attach_seller_data(seller) do
    type_atom = seller.type |> Atom.to_string() |> String.downcase() |> String.to_existing_atom()

    %{seller | seller_data: Map.get(seller, type_atom)}
  end

  defp add_event_permissions(multi, %{type_id: promoter_id, seller_id: seller_id}) do
    promoter_id
    |> get_active_event_ids()
    |> filter_new_event_ids(seller_id)
    |> Enum.with_index()
    |> Enum.reduce(multi, fn {event_id, index}, acc_multi ->
      Multi.insert(
        acc_multi,
        {:event_permission, index},
        SalesPermission.changeset(%SalesPermission{}, %{
          type: :EVENT,
          type_id: event_id,
          seller_id: seller_id
        })
      )
    end)
  end

  defp get_active_event_ids(promoter_id) do
    Repo.all(from(e in Event, where: e.promoter_id == ^promoter_id, select: e.id))
  end

  defp filter_new_event_ids(event_ids, seller_id) do
    existing_event_ids = event_ids |> get_existing_event_permission_event_ids(seller_id) |> MapSet.new()

    event_ids
    |> MapSet.new()
    |> MapSet.difference(existing_event_ids)
    |> MapSet.to_list()
  end

  defp get_existing_event_permission_event_ids(event_ids, seller_id) do
    Repo.all(
      from(sp in SalesPermission,
        where: sp.seller_id == ^seller_id and sp.type == :EVENT and sp.type_id in ^event_ids,
        select: sp.type_id,
        distinct: true
      )
    )
  end

  defp seller_base_query(user_id) do
    user_id_field =
      case Ecto.UUID.cast(user_id) do
        {:ok, _user_id} -> :user_id
        _ -> :user_document_id
      end

    from(s in Seller,
      join: sp in SellerPermission,
      on: sp.seller_id == s.id,
      where: field(sp, ^user_id_field) == ^user_id,
      left_join: store in Store,
      on: s.type == :STORE and store.id == s.type_id,
      left_join: organizer in Organizer,
      on: s.type == :ORGANIZER and organizer.id == s.type_id,
      left_join: address in Address,
      on: s.type == :STORE and address.id == store.address_id,
      left_join: promoter in Promoter,
      on: s.type == :ORGANIZER and promoter.id == organizer.promoter_id,
      left_join: promoter_address in Address,
      on: s.type == :ORGANIZER and promoter_address.id == promoter.address_id,
      preload: [
        store: {store, address: address},
        organizer: {organizer, promoter: {promoter, address: {promoter_address, :country}}}
      ],
      distinct: s.id
    )
  end
end
