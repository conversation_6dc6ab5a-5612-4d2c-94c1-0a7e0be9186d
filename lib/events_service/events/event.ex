defmodule EventsService.Events.Event do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query, warn: false

  alias EventsService.Events.Artist
  alias EventsService.Events.DonationEvents
  alias EventsService.Events.Event
  alias EventsService.Events.EventCounter
  alias EventsService.Events.EventPermission
  alias EventsService.Events.Fee
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.Variant
  alias EventsService.Location.Venue
  alias EventsService.Repo
  alias EventsService.Vendor.Promoter

  # Allowed text characters for name and description.
  # Only letters, numbers, spaces, and the following special characters are allowed:
  # + # & - / ( ) ! . : ? ' " , ‚ ’ ‘ „ “
  @allowed_text_characters ~r/^[\p{L}\p{N}\s\+#&\-\/\(\)!.:?'",‚’‘„“]*$/u

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "events" do
    field :description, :string
    field :title, :string
    field :category, :string
    field :firestore_id, :string
    field :is_visible, :boolean, default: false
    field :is_approved, :boolean, default: false
    field :subtitle, :string
    field :plan_id, :string, default: "base"
    field :thumbnail_url, :string
    field :cover_url, :string
    field :ticket_color, :string
    field :use_event_balance_account, :boolean, default: true
    field :start_date, :utc_datetime
    field :end_date, :utc_datetime
    belongs_to :venue, Venue
    belongs_to :promoter, Promoter
    field :balance_account_id, :string
    field :created_by, :binary_id
    field :created_by_document_id, :string
    field :admission_date, :utc_datetime
    field :box_office_opening_date, :utc_datetime
    field :kickback, :integer, default: 0
    field :chart_key, :string
    field :chart, :map, virtual: true
    field :published_date, :utc_datetime
    field :closed_at, :utc_datetime
    field :closed_by, :binary_id
    field :closed_by_document_id, :string
    field :is_future_demand, :boolean, default: false
    field :deleted_at, :utc_datetime
    field :is_draft, :boolean, default: true
    field :slug, :string
    field :short_code, :string
    field :open_end?, :boolean, default: false, source: :is_open_end
    has_many :ticket_categories, TicketCategory
    has_many :variants, Variant
    has_many :guestlists, EventsService.Guestlists.Guestlist
    has_many :fees, Fee
    has_many :artists, Artist
    has_many :permissions, EventPermission
    has_many :donations, DonationEvents
    has_many :tracking_pixels, EventsService.Tracking.TrackingPixel
    has_many :tracking_links, EventsService.Tracking.TrackingLink
    has_many :seat_comment_groups, EventsService.SeatComments.SeatCommentGroup
    has_many :channel_configs, EventsService.Channels.ChannelConfig
    has_many :entrance_areas, EventsService.Events.EntranceArea
    has_many :platform_fees, EventsService.Accounting.PlatformFee
    has_one :event_counter, EventsService.Events.EventCounter
    timestamps()
  end

  # styler:sort
  @type t ::
          %Event{
            __meta__: Ecto.Schema.Metadata.t(),
            admission_date: DateTime.t(),
            artists: [struct()],
            balance_account_id: String.t(),
            box_office_opening_date: DateTime.t(),
            category: String.t(),
            channel_configs: [struct()],
            chart_key: String.t() | nil,
            closed_at: DateTime.t(),
            closed_by: Ecto.UUID.t(),
            closed_by_document_id: String.t(),
            cover_url: String.t(),
            created_by: Ecto.UUID.t(),
            created_by_document_id: String.t(),
            deleted_at: DateTime.t(),
            description: String.t(),
            donations: [struct()],
            end_date: DateTime.t(),
            event_counter: struct(),
            fees: [struct()],
            firestore_id: String.t(),
            guestlists: [struct()],
            id: Ecto.UUID.t(),
            inserted_at: DateTime.t(),
            is_approved: boolean(),
            is_future_demand: boolean(),
            is_visible: boolean(),
            kickback: integer(),
            open_end?: boolean(),
            permissions: [struct()],
            plan_id: String.t(),
            promoter_id: Ecto.UUID.t(),
            published_date: DateTime.t(),
            seat_comment_groups: [struct()],
            short_code: String.t(),
            slug: String.t(),
            start_date: DateTime.t(),
            subtitle: String.t(),
            thumbnail_url: String.t(),
            ticket_categories: [struct()],
            ticket_color: String.t(),
            title: String.t(),
            tracking_links: [struct()],
            tracking_pixels: [struct()],
            updated_at: DateTime.t(),
            use_event_balance_account: boolean(),
            variants: [struct()],
            venue_id: Ecto.UUID.t()
          }

  @doc false
  def changeset(event, attrs) do
    event
    |> cast(attrs, [
      :firestore_id,
      :is_visible,
      :title,
      :subtitle,
      :description,
      :category,
      :plan_id,
      :thumbnail_url,
      :cover_url,
      :ticket_color,
      :start_date,
      :end_date,
      :venue_id,
      :promoter_id,
      :balance_account_id,
      :admission_date,
      :box_office_opening_date,
      :kickback,
      :chart_key,
      :slug,
      :short_code,
      :published_date,
      :created_by,
      :created_by_document_id,
      :closed_at,
      :closed_by,
      :closed_by_document_id,
      :open_end?,
      :deleted_at
    ])
    |> validate_required([
      :title,
      :description,
      :category,
      :plan_id,
      :thumbnail_url,
      :slug,
      :short_code,
      :cover_url,
      :promoter_id,
      :start_date
    ])
    |> validate_texts()
    |> unique_constraint(:short_code)
    |> validate_user_identification()
    # slug length =  event title (50) + venue name (50) + start date (10) + short code (5) + minus to combine (3)
    |> validate_length(:slug, min: 1, max: 120)
  end

  @doc false
  def update_changeset(event, attrs) do
    event
    |> cast(attrs, [
      :firestore_id,
      :is_visible,
      :title,
      :subtitle,
      :description,
      :category,
      :plan_id,
      :thumbnail_url,
      :cover_url,
      :ticket_color,
      :start_date,
      :end_date,
      :venue_id,
      :balance_account_id,
      :admission_date,
      :box_office_opening_date,
      :kickback,
      :slug,
      :published_date,
      :closed_at,
      :closed_by,
      :closed_by_document_id,
      :open_end?,
      :deleted_at
    ])
    |> validate_required([
      :title,
      :description,
      :category,
      :plan_id,
      :start_date
    ])
    |> validate_texts()
    |> validate_length(:slug, max: 120)
  end

  def publish_draft_changeset(event, attrs) do
    event
    |> cast(attrs, [:is_draft, :closed_at])
    |> validate_required([:is_draft])
    |> validate_draft_status()
  end

  def get(id, preload \\ []) do
    Repo.one(from e in Event, where: e.id == ^id and is_nil(e.deleted_at), preload: ^preload)
  end

  def get_event_with_fees(id) do
    query =
      from e in Event,
        join: f in Fee,
        on: f.event_id == e.id,
        where: e.id == ^id and is_nil(e.deleted_at) and is_nil(f.deleted_at),
        preload: [fees: f]

    Repo.one(query)
  end

  def get_by(attrs) do
    Repo.get_by(__MODULE__, attrs)
  end

  def approve_changeset(event, attrs) do
    cast(event, attrs, [:is_approved])
  end

  def publish_changeset(event, attrs) do
    cast(event, attrs, [:is_visible])
  end

  def future_demand_changeset(event, attrs) do
    cast(event, attrs, [:is_future_demand])
  end

  def finalize_changeset(event, attrs) do
    event
    |> cast(attrs, [
      :closed_at,
      :closed_by,
      :closed_by_document_id
    ])
    |> validate_required(:closed_at)
  end

  def prepare_attrs(attrs \\ %{}) do
    cdn = Application.get_env(:events_service, :cdn)
    gcloud = Application.get_env(:events_service, :gcloud)

    resp =
      %{
        "is_visible" => attrs["visible"],
        "title" => attrs["title"] && String.trim(attrs["title"]),
        "subtitle" => attrs["subtitle"],
        "description" => attrs["description"] && String.trim(attrs["description"]),
        "category" => attrs["category"],
        "plan_id" => attrs["planId"] || "base",
        "thumbnail_url" =>
          attrs["thumbnailPath"] |> String.trim() |> String.replace("#{cdn}/#{gcloud}-cdn-bucket/", ""),
        "cover_url" => attrs["coverPath"] |> String.trim() |> String.replace("#{cdn}/#{gcloud}-cdn-bucket/", ""),
        "start_date" => attrs["startDate"],
        "venue_id" => attrs["venueId"],
        "ticket_color" => attrs["ticketColor"],
        "chart_key" => attrs["chartKey"],
        "admission_date" =>
          case attrs["admissionDate"] do
            nil -> attrs["startDate"]
            value -> value
          end,
        "kickback" =>
          case attrs["kickback"] do
            nil -> nil
            value -> round(value * 100)
          end
      }

    add_end_date_and_open_end(resp, attrs)
  end

  def prepare_update_attrs(attrs, event) do
    %{}
    |> maybe_add_key_value("title", attrs, "title")
    |> maybe_add_key_value("subtitle", attrs, "subtitle")
    |> maybe_add_key_value("category", attrs, "category")
    |> maybe_add_key_value("start_date", attrs, "startDate")
    |> maybe_add_key_value("admission_date", attrs, "admissionDate")
    |> maybe_add_key_value("description", attrs, "description")
    |> maybe_add_key_value("venue_id", attrs, "venueId")
    |> maybe_add_key_value("ticket_color", attrs, "ticketColor")
    |> maybe_add_key_value("slug", attrs, "slug")
    |> maybe_add_path("thumbnail_url", attrs, "thumbnailPath")
    |> maybe_add_path("cover_url", attrs, "coverPath")
    |> maybe_add_kickback(attrs)
    |> add_end_date_and_open_end(attrs, event.start_date)
  end

  def validate_event_sales(event_id) when is_binary(event_id) do
    event_id
    |> get([:event_counter])
    |> validate_event_sales()
  end

  def validate_event_sales(%Event{is_draft: true} = event), do: {:ok, event}
  def validate_event_sales(%Event{event_counter: nil} = event), do: {:ok, event}
  def validate_event_sales(%Event{event_counter: %EventCounter{total_sales: 0}} = event), do: {:ok, event}
  def validate_event_sales(%Event{event_counter: %EventCounter{total_sales: _}}), do: {:error, "Event has sales"}
  def validate_event_sales(nil), do: {:error, "Event not found"}

  def validate_event_not_closed_or_draft(event_id) when is_binary(event_id),
    do: event_id |> get() |> validate_event_not_closed_or_draft()

  def validate_event_not_closed_or_draft(%Event{closed_at: closed_at, is_draft: is_draft} = _event)
      when is_nil(closed_at) or is_draft,
      do: :ok

  def validate_event_not_closed_or_draft(_event), do: {:error, "Event is closed"}

  def check_event_has_seating_plan(event_id) when is_binary(event_id),
    do: event_id |> get() |> check_event_has_seating_plan()

  def check_event_has_seating_plan(%Event{chart_key: nil} = _event), do: {:error, "Event has no seating plan"}
  def check_event_has_seating_plan(nil), do: {:error, "Event not found"}
  def check_event_has_seating_plan(_event), do: :ok

  defp maybe_add_kickback(map, %{"kickback" => nil}), do: Map.put(map, "kickback", nil)
  defp maybe_add_kickback(map, %{"kickback" => value}), do: Map.put(map, "kickback", round(value * 100))
  defp maybe_add_kickback(map, _attrs), do: map

  defp add_end_date_and_open_end(response, attrs), do: add_end_date_and_open_end(response, attrs, nil)

  defp add_end_date_and_open_end(response, %{"openEnd" => true} = attrs, start_date) do
    case attrs["endDate"] do
      nil ->
        start_date = attrs["startDate"] || start_date

        response
        |> Map.put("end_date", DateTime.add(start_date, 12, :hour))
        |> Map.put("open_end?", true)

      end_date ->
        response
        |> Map.put("end_date", end_date)
        |> Map.put("open_end?", true)
    end
  end

  defp add_end_date_and_open_end(response, %{"endDate" => nil} = attrs, start_date) do
    start_date = attrs["startDate"] || start_date

    response
    |> Map.put("end_date", DateTime.add(start_date, 12, :hour))
    |> Map.put("open_end?", true)
  end

  defp add_end_date_and_open_end(response, %{"endDate" => end_date, "openEnd" => open_end}, _start_date) do
    response
    |> Map.put("end_date", end_date)
    |> Map.put("open_end?", open_end)
  end

  defp add_end_date_and_open_end(response, %{"endDate" => end_date}, _start_date) do
    response
    |> Map.put("end_date", end_date)
    |> Map.put("open_end?", false)
  end

  defp add_end_date_and_open_end(response, %{"openEnd" => open_end}, _start_date) do
    Map.put(response, "open_end?", open_end)
  end

  defp add_end_date_and_open_end(response, _attributes, _start_date), do: response

  defp maybe_add_key_value(new_map, new_key, old_map, old_key) when is_map_key(old_map, old_key) do
    case Map.get(old_map, old_key) do
      nil -> Map.put(new_map, new_key, nil)
      value -> Map.put(new_map, new_key, String.trim(value))
    end
  end

  defp maybe_add_key_value(new_map, _new_key, _old_map, _old_key), do: new_map

  defp maybe_add_path(new_map, new_key, old_map, old_key) when is_map_key(old_map, old_key) do
    cdn = Application.get_env(:events_service, :cdn)
    gcloud = Application.get_env(:events_service, :gcloud)

    case Map.get(old_map, old_key) do
      nil -> Map.put(new_map, new_key, nil)
      value -> Map.put(new_map, new_key, value |> String.trim() |> String.replace("#{cdn}/#{gcloud}-cdn-bucket/", ""))
    end
  end

  defp maybe_add_path(new_map, _new_key, _old_map, _old_key), do: new_map

  defp validate_draft_status(changeset) do
    if get_change(changeset, :is_draft),
      do: add_error(changeset, :is_draft, "Event.is_draft cannot be set manually to true"),
      else: changeset
  end

  defp validate_texts(changeset) do
    changeset
    |> validate_length(:title, min: 1, max: 50)
    |> validate_format(:title, @allowed_text_characters)
    |> validate_length(:description, min: 3)
  end

  defp validate_user_identification(changeset) do
    created_by_document_id = get_field(changeset, :created_by_document_id)
    created_by = get_field(changeset, :created_by)

    if is_nil(created_by_document_id) && is_nil(created_by) do
      add_error(changeset, :user_identification, "either created_by_document_id or created_by must be present")
    else
      changeset
    end
  end
end
