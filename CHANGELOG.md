# Changelog

All notable changes to this project will be documented in this file.

## [4.32.0] - 2025-08-21

### Features

- Add sales channels to orders (#687)

## [4.31.0] - 2025-08-20

### Features

- Extend scan api by checkInDate param and adjust ticket history … (#685)

### Bug Fixes

- *(order_controller)* Handle Seatsio book error (#684)
- Returning :ok in process_report to fit the expected response in adyen webhook_controller (#686)
- Truncate iso8601 checkInDate date during ticket scan (#688)

### Miscellaneous Tasks

- *(release)* V4.31.0 (#689)

## [4.30.1] - 2025-08-15

### Other

- Skip webhook completion when feature flag disabled (#682)

### Miscellaneous Tasks

- *(release)* V4.30.1 (#683)

## [4.30.0] - 2025-08-15

### Features

- Add missing personal information data (#674)
- Add gender to personal information response (#677)
- Make async payment process require parameter (#679)

### Bug Fixes

- Handle imported tickets events (#671)
- Support-3d-secure-async-polling (#672)
- *(orders)* Handle webhook jobs with missing PSP reference (#675)
- Match on CREATED order_creation_worker (#676)
- *(orders)* Process order completion from manual captcha webhook (#678)
- Compare "true" with order-params (#680)

### Miscellaneous Tasks

- *(release)* V4.30.0 (#681)

## [4.29.0] - 2025-08-07

### Features

- Add scanCode to tickets API (#669)

### Refactor

- Remove migration code (#668)

### Miscellaneous Tasks

- *(release)* V4.29.0 (#670)

## [4.28.1] - 2025-08-07

### Bug Fixes

- Don't generate v0 ScanCodes (#666)

### Miscellaneous Tasks

- *(release)* V4.28.1 (#667)

## [4.28.0] - 2025-08-07

### Features

- Adding voucher code to ticket csv full_export (#654)
- Save ScanCode for tickets when the ticket is active (#656)
- Use saved scan codes to validate the ticket during scan (#658)
- Add scan codes to ticket (scan) export (#657)
- Use scan_codes instead of generating ticket tokens during runti… (#662)

### Bug Fixes

- Remove int id from scan codes (#660)
- Remove double update tickets function (#661)
- Revert changes on ticket scan export (#664)

### Refactor

- Remove deprecated code and adjust API documentaion (#651)
- Clean up ticket scan (#659)

### Miscellaneous Tasks

- Update PR template (#663)
- *(release)* V4.28.0 (#665)

## [4.27.0] - 2025-08-04

### Features

- Lock all tickets with status created and same variant id during… (#649)
- Add company to internal address DTO that is used for PubSub (#650)

### Bug Fixes

- Create personal information without address information (#652)

### Miscellaneous Tasks

- *(release)* V4.27.0 (#653)

## [4.26.0] - 2025-07-31

### Features

- Add some logging for failed to acquire lock on both ticket and … (#647)

### Bug Fixes

- Apply limit per redemption again to total cart items (#644)
- Change voucher counter to count per order having that voucher instead of per ticket (#646)

### Miscellaneous Tasks

- *(release)* V4.26.0 (#648)

## [4.25.0] - 2025-07-22

### Features

- Update payment method endpoint to support multi languages (#636)
- Add additional fields to orders message (#638)

### Bug Fixes

- *(orders)* Fix flag order as fraud (#635)
- Exclude fraud ticket from valid_tickets_query (#637)
- Ticket counter valid ticket should exclude ticket with status created (#639)
- Existing ticket should not include ticket with status created (#642)

### Miscellaneous Tasks

- Removes uniq job spec from orders monitor
- *(release)* V4.25.0 (#643)

## [4.24.1] - 2025-07-15

### Bug Fixes

- *(otel)* Improve error handling (#630)

### Miscellaneous Tasks

- *(release)* V4.24.1 (#631)

## [4.24.0] - 2025-07-11

### Features

- Increase pool_size to 20 (#627)
- Increase pool_size to 30 (#628)

### Bug Fixes

- Add missing reserved tickets to event counter (#626)

### Miscellaneous Tasks

- *(release)* V4.24.0 (#629)

## [4.23.0] - 2025-07-09

### Features

- *(tracing)* Add instrumentation to pay API (#618)
- Add draft email feature toggle functionality (#623)

### Miscellaneous Tasks

- Config formatter / run mix format(#621)
- *(release)* V4.23.0 (#625)

## [4.22.1] - 2025-07-05

### Bug Fixes

- *(checkout)* Combine payment method fee lists for cart total calculation

### Miscellaneous Tasks

- *(release)* V4.22.1 (#619)

## [4.22.0] - 2025-07-03

### Features

- *(orders)* Recursively split numeric values in price matrix (#610)
- *(orders)* Enqueue job to update ticket counters for orders (#567)
- *(alerting)* Monitor order success rates (#613)
- *(monitoring)* Improve orders monitoring worker (#615)

### Bug Fixes

- *(pay)* Postal code validation (#614)
- *(ticketswap)* Add error handling for non-stagedates tickets (#531)

### Refactor

- Remove release toggle platform_fees_v3 and dead code (#616)

### Performance

- *(database)* Add performance indexes for ticket queries (#559)

### Miscellaneous Tasks

- Run formatter
- *(release)* V4.22.0 (#617)

## [4.21.0] - 2025-06-27

### Features

- Add sql queries to export an event and restore dev DB (#603)
- *(otlp)* Enable otlp on production env (#611)

### Bug Fixes

- Making bill and other properties optional in ticket export to ma… (#608)

### Miscellaneous Tasks

- Adjust GitHub PR template (#609)
- *(release)* V4.21.0 (#612)

## [4.20.0] - 2025-06-24

### Features

- Add entrance area info to ticket_json for ticket print (#604)

### Refactor

- Remove expensive outer join (#606)

### Miscellaneous Tasks

- *(release)* V4.20.0 (#607)

## [4.19.0] - 2025-06-17

### Features

- Extract seller info for user token (#592)
- *(orders)* Add seller address to personal information (#602)

### Miscellaneous Tasks

- *(release)* V4.19.0 (#605)

## [4.18.2] - 2025-06-13

### Bug Fixes

- Allow checkout for draft events (#600)

### Miscellaneous Tasks

- *(release)* V4.18.2 (#601)

## [4.18.1] - 2025-06-13

### Bug Fixes

- Use correct tax rate for shipping fee (#595)
- Don't add payment method fees to the carts total (#597)
- Add fallback for requests without payment method (#598)

### Miscellaneous Tasks

- *(release)* V4.18.1 (#599)

## [4.18.0] - 2025-06-12

### Features

- Add PullRequest Template (#594)

### Miscellaneous Tasks

- *(release)* V4.18.0 (#596)

## [4.17.0] - 2025-06-11

### Features

- Allow order for seats without hold token (#591)

### Bug Fixes

- Show checkin date on ticket response (#590)

### Miscellaneous Tasks

- *(release)* V4.17.0 (#593)

## [4.16.0] - 2025-06-11

### Features

- *(platformFee)* Use platform fees (#582)

### Refactor

- Clean up migration code (#587)

### Miscellaneous Tasks

- *(release)* V4.16.0 (#588)

## [4.15.0] - 2025-06-05

### Features

- Handle pending orders manually (#583)
- Reset TIMEDOUT orders (#584)

### Bug Fixes

- Add actor_id and actor_document_id to ticket and order histories (#586)

### Miscellaneous Tasks

- *(release)* V4.15.0 (#585)

## [4.14.1] - 2025-06-05

### Bug Fixes

- Adding nil match for valid_email? (#580)

### Miscellaneous Tasks

- *(release)* V4.14.1 (#581)

## [4.14.0] - 2025-06-05

### Features

- *(refund)* Refund tickets without order (#578)

### Bug Fixes

- Adding swapped tickets to event ticket exports (#570)

### Miscellaneous Tasks

- *(release)* V4.14.0 (#579)

## [4.13.0] - 2025-06-03

### Features

- Add gke_endpoint config to ex_service_client (#575)

### Miscellaneous Tasks

- *(release)* V4.13.0 (#576)

## [4.12.3] - 2025-06-03

### Bug Fixes

- Bo orders should be set to refunded if all tickets from this order are refunded (#573)

### Miscellaneous Tasks

- *(release)* V4.12.3 (#574)

## [4.12.2] - 2025-06-02

### Bug Fixes

- Correct orders.orders pubsub message and addmission_date prop (#571)

### Miscellaneous Tasks

- *(release)* V4.12.2 (#572)

## [4.12.1] - 2025-05-28

### Bug Fixes

- Showing ticketswap tickets in attendee list (#564)
- Removing filter_for_order_status(:PAID) from count_used_query_for_event to add swapped tickets to checkIn event counter (#568)

### Miscellaneous Tasks

- *(release)* V4.12.1 (#569)

## [4.12.0] - 2025-05-28

### Features

- *(workers)* Asynchronous ticket counter updates (#561)

### Miscellaneous Tasks

- *(release)* V4.12.0 (#565)

## [4.11.2] - 2025-05-27

### Miscellaneous Tasks

- Bump version

## [4.11.1] - 2025-05-27

### Bug Fixes

- Add swapped tickets to sales stats (#562)
- Add swapped tickets to sold query

### Miscellaneous Tasks

- *(release)* V4.11.1 (#563)

## [4.11.0] - 2025-05-27

### Features

- *(DB)* Add db dump to set up a new database more easily (#555)
- Search by short_uuid (#558)
- *(accounting)* Create PlatformFees and Accounting context (#551)

### Miscellaneous Tasks

- *(release)* V4.11.0 (#560)

## [4.10.0] - 2025-05-23

### Features

- Add new ticket type (#554)
- Adding a4 print suffix to ticket token on print publish (#556)

### Miscellaneous Tasks

- *(release)* V4.10.0 (#557)

## [4.9.0] - 2025-05-22

### Features

- Add seat to ticket response (#547)
- Add mlpm name to ticket (#550)
- Adding gzip compression to configs (#552)

### Miscellaneous Tasks

- *(release)* V4.9.0 (#553)

## [4.8.3] - 2025-05-21

### Bug Fixes

- Don't count and calculate swapped tickets as pending (#548)

### Miscellaneous Tasks

- *(release)* V4.8.3 (#549)

## [4.8.2] - 2025-05-21

### Bug Fixes

- Setting actual seller name in order_summary_data (#539)
- Update service_client to 1.9.1 (#540)
- Add receiptNumber to order_summary_data dto (#542)
- *(seats)* Ignore channels on seats release (#544)
- *(eventCounter)* Add swapped tickets to total sales calculation (#545)

### Miscellaneous Tasks

- *(release)* V4.8.2 (#546)

## [4.8.1] - 2025-05-19

### Features

- Remove hardcoded ticket swap mail for entrance change mail. send entrance change to ticket swap attendee (#536)

### Bug Fixes

- Publish order update message on ticket refund (#534)
- Extend attendee search by parsing the query string and searching… (#537)

### Miscellaneous Tasks

- *(release)* V4.8.1 (#538)

## [4.8.0] - 2025-05-14

### Features

- Add order_id to seatsio book process (#532)
- *(voucher)* Add min items to voucher (#533)

### Bug Fixes

- Fetching seller info from events service for summary pdf (#530)

### Miscellaneous Tasks

- *(release)* V4.8.0 (#535)

## [4.7.0] - 2025-05-12

### Features

- Fetch seats `already_booked` error (#526)

### Miscellaneous Tasks

- *(release)* V4.7.0 (#529)

## [4.6.0] - 2025-05-12

### Features

- *(order)* Add order fee to bill data (#515)
- *(refunds)* Implement advanced filtering for refund transactions (#514)
- *(ticket)* Add refund transactions to ticket response (#521)
- *(router)* Add snake_case middleware to refunds scope (#522)
- Add ticket status to ticket response (#524)
- Create imported tickets (#508)

### Bug Fixes

- *(refunds)* Use ticket_ids and add proper params validation (#523)
- *(refund)* Insufficient_funds pattern match (#525)
- Add order publish to process_draft_order to be able to print PDF (#527)

### Miscellaneous Tasks

- *(release)* V4.6.0 (#528)

## [4.5.0] - 2025-05-08

### Features

- *(address_json)* Improve address data handling (#505)
- *(order)* Add bill details to order response (#507)
- *(refunds)* Allow seller to refund tickets (#503)
- *(ticket)* Add receipt_number field to order response (#511)
- 4661 scoped vouchers (#509)
- *(order_controller)* Add support for optional seller details in order (#516)

### Bug Fixes

- *(orders)* Add limit to order query and update order ID parameter (#504)
- Bump styler (#512)
- Apply new voucher logic to pay API (#518)
- Add error handling for not clearly assignable vouchers in pay API (#519)

### Refactor

- Remove unused code (#510)
- Move private functions (#517)

### Miscellaneous Tasks

- Ticket read permission for ticket view (#506)
- *(refund)* Add proper spec (#513)
- *(release)* V4.5.0 (#520)

## [4.4.1] - 2025-05-02

### Bug Fixes

- *(order)* Optimize pagination query to avoid issues with group_by clause (#500)
- Adjust scan API to return correct max attendees total

### Other

- Use correct dates for attendees list API

### Miscellaneous Tasks

- Upgrade ex_service_client to version 1.4.0 (#499)
- *(release)* V4.4.1 (#502)

## [4.4.0] - 2025-05-02

### Features

- Auth casdoor users (#471)

### Refactor

- Add some logging information to scan API (#497)

### Miscellaneous Tasks

- *(release)* V4.4.0 (#498)

## [4.3.0] - 2025-04-29

### Features

- Remove CloudRun deployment for dev environment (#492)

### Bug Fixes

- Sync variant and ticket category counter after swap a ticket (#494)

### Miscellaneous Tasks

- *(release)* V4.3.0 (#495)

## [4.2.8] - 2025-04-14

### Bug Fixes

- *(orders)* Update order price matrix fee apply to field (#490)

### Miscellaneous Tasks

- *(release)* V4.2.8 (#491)

## [4.2.7] - 2025-04-12

### Bug Fixes

- Reset unused tickets during reset a ticket category

### Miscellaneous Tasks

- *(release)* V4.2.7

## [4.2.6] - 2025-04-11

### Refactor

- Add some critical loggings to orders process (#488)
- Clean up scan API (#487)

### Miscellaneous Tasks

- *(release)* V4.2.6 (#489)

## [4.2.5] - 2025-04-09

### Bug Fixes

- Increase process_pending_order performance by making publish_tic… (#483)
- Adding timestamp type: :utc_datetime to ticket history (#484)
- Handle :ticket_has_no_voucher return

### Refactor

- Remove migration code (SQL Query) (#482)

### Miscellaneous Tasks

- *(release)* V4.2.5 (#486)

## [4.2.4] - 2025-04-07

### Bug Fixes

- *(orders)* Round fee amount to 2 decimal places (#480)

### Miscellaneous Tasks

- *(release)* V4.2.4 (#481)

## [4.2.3] - 2025-04-07

### Bug Fixes

- *(orders)* Round fee amount calculation (#478)

### Miscellaneous Tasks

- *(release)* V4.2.3 (#479)

## [4.2.2] - 2025-04-04

### Miscellaneous Tasks

- *(release)* V4.2.1 (#476)
- *(release)* V4.2.2 (#477)

## [4.2.1] - 2025-04-04

### Bug Fixes

- Skip to_float for internal cart total calculation (#473)

### Miscellaneous Tasks

- *(release)* V4.2.1 (#474)

## [4.2.0] - 2025-04-03

### Features

- Add platform fee "apply to" configuration (#470)

### Miscellaneous Tasks

- *(release)* V4.2.0 (#472)

## [4.1.0] - 2025-04-02

### Features

- Add ticketLabel to admission API (#468)

### Bug Fixes

- Remove typo

### Miscellaneous Tasks

- *(release)* V4.1.0 (#469)

## [4.0.0] - 2025-04-02

### Features

- [**breaking**] Use ticket category quota management (#465)

### Miscellaneous Tasks

- *(release)* V4.0.0 (#467)

## [3.5.0] - 2025-04-02

### Features

- Adding mlp name to ticket exports (#447)
- Adding mlpm label to my-tickets API (#463)
- Percentage based fee calculation (#461)

### Bug Fixes

- *(orders)* Add support for float amounts in order price matrix (#464)

### Miscellaneous Tasks

- *(release)* V3.5.0 (#466)

## [3.4.1] - 2025-03-26

### Bug Fixes

- Convert Money type correct to fix event based platform fees

### Miscellaneous Tasks

- *(release)* V3.4.1 (#460)

## [3.4.0] - 2025-03-25

### Features

- *(mlp)* Add ticket quota and sync from events-service (#454)
- Add distrtibution infos to ticket (#457)

### Bug Fixes

- Use correct data_type for DB PK (#458)

### Miscellaneous Tasks

- Remove gitops (#455)
- *(release)* V3.4.0 (#459)

## [3.3.0] - 2025-03-20

### Features

- Adding first and lastname to ticket export | update translations (#452)

### Miscellaneous Tasks

- *(release)* V3.3.0 (#453)

## [3.2.1] - 2025-03-20

### Features

- Change ticket swap sender and recipient notification mail (#445)
- Add kubectl current-context to mirrord script (#448)

### Bug Fixes

- Valid swappable ticket should not have their end date greater th… (#444)
- Change PubSub configuration

### Miscellaneous Tasks

- *(release)* V3.2.0 (#446)
- *(release)* V3.2.0
- *(release)* V3.2.1 (#451)

## [3.1.1] - 2025-03-18

### Bug Fixes

- Donnot use PubSub emulator for dev and prod (#442)

### Miscellaneous Tasks

- *(release)* V3.1.1 (#443)

## [3.1.0] - 2025-03-18

### Features

- *(pubsub)* Use PubSub emulator in minikube(#435)

### Bug Fixes

- Prevent http status 500 error (#434)
- Personal info adding unique_constraint (#436)
- Replace Mix.env with config_env
- Add missing import Config
- Change children list in application
- Use OrdersService.Goth (#439)
- Configure GH action `test` (#440)

### Refactor

- Clean up application.ex and add logging (#438)

### Miscellaneous Tasks

- *(release)* V3.1.0 (#441)

## [3.0.1] - 2025-03-14

### Bug Fixes

- Remove typo

### Miscellaneous Tasks

- *(release)* V3.0.1 (#433)

## [3.0.0] - 2025-03-13

### Features

- *(tc_counter)* Add ticket category counter (#429)
- *(router)* [**breaking**] Remove deprecated APIs (#431)

### Miscellaneous Tasks

- *(release)* V3.0.0 (#432)

## [2.13.0] - 2025-03-11

### Features

- Deactivate deprecated APIs (#414)
- *(country)* Add phonePrefix to countries API (#427)

### Miscellaneous Tasks

- *(release)* V2.13.0 (#428)

## [2.12.0] - 2025-03-10

### Features

- Use BACKEND_URL ENV to configure service client (#421)

### Miscellaneous Tasks

- *(release)* V2.12.0 (#426)

## [2.11.1] - 2025-03-10

### Bug Fixes

- Use correct dependencies for the GKE deployment (#424)

### Miscellaneous Tasks

- *(release)* V2.11.1 (#425)

## [2.11.0] - 2025-03-07

### Features

- *(my-tickets)* Add subtitle to event (#422)

### Miscellaneous Tasks

- *(release)* V2.11.0

## [2.10.1] - 2025-03-05

### Bug Fixes

- Extend ticket_exporter.ex to get city and postal_code from order.created_by_personal_information

### Miscellaneous Tasks

- Format
- *(release)* V2.10.1

## [2.10.0] - 2025-03-04

### Features

- Add makefile and GitHub Actions workflow for release management (#413)

### Bug Fixes

- Reduce log level for client errors
- Add get_event_fees(item) to cart_total function again | fix kickback calculation for services_calculate_item_total
- Add 0 fallback for get_event_fees kickback (#417)

### Miscellaneous Tasks

- Format
- *(release)* V2.10.0

## [2.9.1] - 2025-02-27

### Bug Fixes

- Add workaround to valid addresses with broken API calls (#412)

### Other

- Add some logging (#411)

### Miscellaneous Tasks

- Update version

## [2.9.0] - 2025-02-27

### Features

- Add new datetime string formatting (#409)

### Miscellaneous Tasks

- Bump to version 2.9.0 (#410)

## [2.8.3] - 2025-02-26

### Bug Fixes

- Handle addresses during checkout (#406)

### Other

- Use Address type to map client input (#407)

### Miscellaneous Tasks

- Udpate patch version (#408)

## [2.8.2] - 2025-02-25

### Bug Fixes

- Update service_client to use retries (#404)

### Miscellaneous Tasks

- Update patch version (#405)

## [2.8.1] - 2025-02-20

### Features

- *(ticket_token)* Implement V2 ticket token format (#388)
- *(ticket_history)* Add metadata handling and sort schema fields (#393)
- *(ticket)* Add ticket type (#394)
- Use git ops for changelogs (#397)
- Add flag to countries API (#395)
- *(db)* Create some unique indexe for countries table (#400)

### Documentation

- Add swagger documentation for paymants API (#396)

### Miscellaneous Tasks

- Release version v2.7.0 (#398)
- Release version v2.8.0 (#401)
- Format cents without currency (#399)
- Bump version to v2.8.1 (#402)

## [2.6.7] - 2025-02-13

### Features

- Add ticket type to ticket response
- Remove saels_period_id from tickets (#391)

### Bug Fixes

- *(validation)* Add error for missing seat configs for channel key (#386)

### Documentation

- Fix typo in country schema

### Miscellaneous Tasks

- Update patch version to v2.6.6 (#392)

## [2.6.6] - 2025-02-10

### Features

- Add country iso to internal data (#383)

### Miscellaneous Tasks

- Update patch version to v2.6.5 (#384)
- Update patch version to v2.6.6 (#385)

## [2.6.5] - 2025-02-05

### Refactor

- Improve postal code search functionality by ordering starting first (#382)

## [2.6.4] - 2025-02-04

### Bug Fixes

- Add user_document_id to personal_information (#380)

### Miscellaneous Tasks

- Update patch version to v2.6.4 (#381)

## [2.6.3] - 2025-02-03

### Bug Fixes

- Use correct check for future demand (#378)

### Miscellaneous Tasks

- Update patch version to v2.6.3 (#379)

## [2.6.2] - 2025-02-03

### Features

- Add delivery address to order details (#366)
- Add location to personal information (#367)
- Book/release tables (#359)
- *(seats)* Support multiple hold tokens (#369)
- *(cloudbuild)* Update image location (#368)
- Expand internal personal information (#374)

### Bug Fixes

- Use preload list to publish pubsub message (#365)
- *(personal_information)* Fix email validation regex (#371)
- Add email to order again (#375)

### Documentation

- Fix typo in API documentaion (#372)

### Miscellaneous Tasks

- Bump version to v2.6.0 (#370)
- Bump version to v2.6.1 (#373)
- Update patch version to v2.6.2 (#377)

## [2.5.2] - 2025-01-23

### Miscellaneous Tasks

- Update patch version to v2.5.2

## [2.5.1] - 2025-01-23

### Features

- Add logger for notification and alerting on ticket swapp operations (#364)
- Add delivery address to internal order data and PubSub (#361)

### Bug Fixes

- Add nil check to delivery address
- Maybe read delivery address id from multi

### Miscellaneous Tasks

- Update patch version to v2.5.1

## [2.5.0] - 2025-01-23

### Bug Fixes

- Check if variant is seatsio (#355)
- *(seatsio)* Update keys (#356)
- Use seats release queue (#357)

### Miscellaneous Tasks

- Use GitHub variable to configure reviewer-lottery (#358)
- Update minor version to v2.5.0 (#363)

## [2.4.1] - 2025-01-10

### Features

- Use cached apis (#349)

### Bug Fixes

- Update order status handling in maybe_revert_order_status (#350)
- Add opentelemetry sampler + propagator config (#351)
- Update Elixir setup and clean up workflow file (#352)

### Other

- *(version)* Update version to 2.4.1 in mix.exs (#354)

### Refactor

- Simplify OpenTelemetry setup for all environments (#353)

## [2.4.0] - 2025-01-08

### Features

- Book/release seats (#284)
- Add promoter check to pay API (#344)
- Activate open telemetry only for dev (#345)

### Bug Fixes

- Do not use pattern match in pay API (#347)

### Refactor

- Rename method to clarify order filtering logic (#341)

### Miscellaneous Tasks

- Update ex_service_client to version 0.8.3 (#342)
- *(version)* Update version to v2.4.0 (#346)

## [2.3.8] - 2025-01-02

### Miscellaneous Tasks

- Update patch version to v2.3.8 (#340)

## [2.3.7] - 2025-01-02

### Bug Fixes

- Adyen return a map not a struct (#337)

### Miscellaneous Tasks

- Bump to version 2.3.7 (#338)

## [2.3.6] - 2024-12-23

### Features

- Split refund event handler into reversal and refund for correctness (#335)

### Miscellaneous Tasks

- Bump version (#336)

## [2.3.5] - 2024-12-22

### Bug Fixes

- Use correct alias

### Miscellaneous Tasks

- Update patch version

## [2.3.4] - 2024-12-19

### Bug Fixes

- Use kickback from promoter_kickback (#333)

### Miscellaneous Tasks

- *(version)* Update version to v2.3.4 (#334)

## [2.3.3] - 2024-12-19

### Bug Fixes

- Add alias to each ticket SQL query (#331)

### Miscellaneous Tasks

- Update patch version (#332)

## [2.3.2] - 2024-12-19

### Bug Fixes

- Add naming to sql queries (#327)
- Add select to extras query (#328)
- Move where condition in to the DB query (#329)

### Miscellaneous Tasks

- Update patch version (#330)

## [2.3.1] - 2024-12-16

### Bug Fixes

- Add nil check for personal_information_from_order_item to allow orders for promoter accounts (#323)

### Miscellaneous Tasks

- Update patch version (#324)

## [2.3.0] - 2024-12-12

### Features

- Allow scan for created tickets
- Allow check in for created tickets

### Bug Fixes

- Add CANCEL_OR_REFUND to refund event handlers list (#317)
- Remove redundant event_code check from finalize_failed_refund (#318)
- Pattern matching on refund transaction (#319)
- Also just return `:ok` for order refund (#320)
- Move CANCEL_OR_REFUND to trigger finalize_successful_refund (#321)

### Miscellaneous Tasks

- Update version to v2.3.0 (#322)

## [2.2.3] - 2024-12-10

### Bug Fixes

- Update ex_service_client to version 0.8.2 and bump version to 2.2.3 (#315)

## [2.2.1] - 2024-12-10

### Features

- Add experimental OpenTelemetry (#307)
- [**breaking**] Add bulk create for tickets without order (#309)

### Miscellaneous Tasks

- Update patch version to v2.2.1 (#313)

## [2.2.0] - 2024-12-06

### Features

- One more ticket swap status adjustment (#306)
- No event fee and kickback for extras (#304)

### Miscellaneous Tasks

- Update minor version to v2.2.0 (#308)

## [2.1.1] - 2024-12-04

### Features

- Extend cloudbuild.yaml to init translation submodule

### Bug Fixes

- Correct cloudbuild.yaml step ids
- Use correct variant for total variant quota (#303)

### Miscellaneous Tasks

- Update ex_service_client
- Update patch version

## [2.1.0] - 2024-12-04

### Features

- One more ticket swap status adjustment (#296)
- [**breaking**] Remove sales_periods from service (#294)

### Bug Fixes

- Refactor categoryId logic to variantId logic (#297)
- Refactor categoryId logic to variantId logic (#299)
- Refactor categoryId logic to variantId logic (#300)

### Miscellaneous Tasks

- Remove redundant ticket validation (#295)
- Update minor version to v2.1.0 (#301)

## [2.0.0] - 2024-11-26

### Bug Fixes

- Add future_demand_fee to services_totals

### Miscellaneous Tasks

- [**breaking**] Update major version (#291)

## [1.16.0] - 2024-11-20

### Features

- Introduce basic order validation and validation error handling (#266)
- Add ticket export translations
- *(ticket)* Include seat information in ticket response (#287)

### Bug Fixes

- Set order as refunded (#283)
- Properly calculate fees and total for tickets csv export
- Add enum to spi spec
- Add enum to spi spec
- Cleanup config.exs | change calculate_total guard to is_number
- Send VariantCounter after paid an order (#286)
- Publish ticket update (#288)

### Miscellaneous Tasks

- Remove submodule
- Format
- Resolve comments
- Bump version to 1.16.0

## [1.15.4] - 2024-11-15

### Bug Fixes

- Ignore pubsub messages in minikube
- Remove failed tickets from variant counter (#281)

### Miscellaneous Tasks

- Upate patch version (#282)

## [1.15.3] - 2024-11-15

### Features

- *(variant_counter)* Expand variant counter with sold_items, reserved_items, exising_items (#270)

### Bug Fixes

- Use correct ticket status for WiP refunding tickets (#275)
- Don't count timed_out or deleted tickets (#276)
- Don't count invitation rejected tickets (#277)

### Other

- Remove unknown column deleted_at from tickets query (#278)

### Miscellaneous Tasks

- Update patch version to v1.15.3 (#279)

## [1.15.2] - 2024-11-14

### Bug Fixes

- Make sales_period and category nullable in tickets but variant required (#272)

### Refactor

- Reorganize private order processing functions (#273)

### Miscellaneous Tasks

- Update patch version to v1.15.2 (#274)

## [1.15.1] - 2024-11-14

### Bug Fixes

- Creating entrance change oban jobs in multi instead of iterative
- Unused variable
- Adding repo to transaction
- Removing repo from oban insert
- Replace variant status with availability in order validation (#265)
- *(config)* Change environment to local for dev setup (#267)

### Miscellaneous Tasks

- Add git cliff to giignore (#262)
- Update patch version to v1.15.1 (#271)

## [1.15.0] - 2024-11-11

### Bug Fixes

- Move cldr config from runtime to config to use it during compile time (#259)
- Move money config from runtime to config to use it during compile time

### Miscellaneous Tasks

- Bump version to v1.14.0 (#258)
- Upate minor version to v1.15.0 (#261)

## [1.14.0] - 2024-11-07

### Features

- *(ticket-history)* Internal count api (#255)

### Miscellaneous Tasks

- Bump version to v1.13.0 (#256)
- Change wrong entrance area response (#257)

## [1.13.0] - 2024-11-06

### Features

- Add Jan to Reviewer Lottery (#251)
- *(entrance-area)* Check entrance area on scan (#252)

### Bug Fixes

- Add category flag 'allEntranceAreasAllowed' for tickets mail

### Miscellaneous Tasks

- Use experimental scan/check-in api (#250)

## [1.12.4] - 2024-11-05

### Bug Fixes

- *(order)* Trigger refund for payed but timedout orders (#249)

### Refactor

- Event permission check (#248)

### Miscellaneous Tasks

- Clean up README
- Update patch version

## [1.12.2] - 2024-10-31

### Features

- *(SD1-3457)* Modify scan api responses (#235)

### Bug Fixes

- Increase pubsub publish logging and error handling
- Remove config_env check on pubsub functions | check is still made in GooglePubsubAdapter.publish

### Styling

- Remove typo in logger output (#244)

### Miscellaneous Tasks

- Bump version to 1.12.2

## [1.12.1] - 2024-10-30

### Bug Fixes

- Set location type to entrance area only if entrance area id is i… (#239)
- *(order)* Add email validation and improve validation of personal data (#240)
- *(order)* Add missing validation response (#241)

### Refactor

- Use location id and location type instead of entrance area id (#234)

### Miscellaneous Tasks

- Update version to v1.12.1 (#242)

## [1.12.0] - 2024-10-30

### Miscellaneous Tasks

- Adjust status code as requested by ticket swap (#237)
- Update minor version to v1.12.0 (#238)

## [1.11.3] - 2024-10-30

### Features

- Added location_type field (#233)

### Miscellaneous Tasks

- Update patch version to v1.11.3 (#236)

## [1.11.2] - 2024-10-28

### Features

- *(my-tickets)* Show entrance areas (#219)
- Add pubsub handler for ticket category entrance area change to publish change emails
- *(ticket-history)* Add entrance area id on scan (#223)
- Add category id to attendee response (#226)
- Use db_seeds instead of phil_columns (#227)

### Bug Fixes

- Import OrdersService.Pubsub.Handler.UpdateTicketCategoriesHandler in message_handler
- Change entrance area change message type to entranceAreaChange
- Adding entranceAreas to entrance area ticket mail data
- Add experimental plug to scan/check in (#225)
- Remove typo in variablename (#229)
- Add ticket.purchase_date index (#231)

### Refactor

- Read service port from an env variable (#228)

### Miscellaneous Tasks

- Resolve pr comments
- Resolve pr comments
- Resolve pr comments
- Update patch version to v1.11.2 (#232)

## [1.11.1] - 2024-10-22

### Bug Fixes

- Add nil-check to personal information (#216)

### Miscellaneous Tasks

- Udpate patch version to v1.11.1 (#217)

## [1.11.0] - 2024-10-22

### Features

- Validate scanning permissions (#209)
- Add entrance area id to ticket histories (#213)

### Bug Fixes

- Check in date for unused tickets (#212)
- Remove typo (#214)

### Miscellaneous Tasks

- Update service client version (#210)
- *(entrance-area)* Rename entrance area auth error code (#211)
- Update minor version to v1.11.0 (#215)

## [1.10.3] - 2024-10-11

### Bug Fixes

- Use correct String check (#208)

### Miscellaneous Tasks

- Bump version to v1.10.3

## [1.10.2] - 2024-10-11

### Bug Fixes

- Add birthdate to required personal information attributes (#206)

### Miscellaneous Tasks

- Update patch version to v1.10.2 (#207)

## [1.10.0] - 2024-10-02

### Features

- Add casdoor auth (#201)

### Bug Fixes

- Configure CORS Plug, allow  x-api-experimental header (#200)

### Refactor

- Use secrets lib (#202)

### Miscellaneous Tasks

- Update minor version to v1.10.0 (#203)

## [1.9.3] - 2024-09-30

### Bug Fixes

- Add missing error handling to checkin API (#199)

### Miscellaneous Tasks

- Update patch version to v1.9.3

## [1.9.2] - 2024-09-27

### Features

- *(orders,svc,gke)* Add annotated gke deployment [SI-223][SI-220] (#197)

### Bug Fixes

- Add missing error handling to scan api (#198)

### Miscellaneous Tasks

- Update patch version to v1.9.2

## [1.9.1] - 2024-09-23

### Bug Fixes

- Add missing message type (#193)
- Clean up scan API and add some error handling (#195)

### Miscellaneous Tasks

- Add tzdata to mix.exs
- Update patch verstion to v1.9.1

## [1.9.0] - 2024-09-20

### Bug Fixes

- Mark ticket as fraud internal server error we do not need to pass the history anymore since it is directly preloaded with the ticket. (#192)

### Miscellaneous Tasks

- Update ex_ikarus to 1.1.0 (#191)
- Update patch version to v1.9.10
- Update minor version to v1.9.0

## [1.8.9] - 2024-09-13

### Bug Fixes

- Return unique orders (#190)

### Miscellaneous Tasks

- Update version to 1.8.9

## [1.8.8] - 2024-09-13

### Other

- Temporarily remove rate limit plug

### Miscellaneous Tasks

- Bump version

## [1.8.7] - 2024-09-11

### Miscellaneous Tasks

- Error_code + bump version

## [1.8.6] - 2024-09-11

### Miscellaneous Tasks

- Improve api response for rate limit
- Bump version

## [1.8.5] - 2024-09-11

### Bug Fixes

- Add rate limit to legacy scan API

### Miscellaneous Tasks

- Bump version 1.8.5

## [1.8.4] - 2024-09-11

### Bug Fixes

- Patter match on string true in rate limit plug

### Miscellaneous Tasks

- Bump version

## [1.8.3] - 2024-09-11

### Bug Fixes

- Add some inspect to logger output

### Miscellaneous Tasks

- Update patch version to v1.8.3

## [1.8.2] - 2024-09-11

### Other

- Rate limiting plug camelCase `isDraft`

## [1.8.1] - 2024-09-09

### Bug Fixes

- Prevent create empty order (#188)

### Other

- Fix `classify_events` if "isDraft" non-existent (#187)

### Miscellaneous Tasks

- Update version to 1.8.1

## [1.8.0] - 2024-09-09

### Features

- Order sim search (#173)
- *(api)* List tickets of order (#175)
- Sync orders on fd event update (#180)
- Bulk update orders (#184)

### Bug Fixes

- Change fd event update type (#183)
- Read basic auth creds during runtime (#186)

### Miscellaneous Tasks

- Update patch version to v1.7.3
- Update version to 1.8.0

## [1.7.2] - 2024-09-02

### Features

- Add ex_ikarus (#174)

### Bug Fixes

- Get ADYEN_ENVIRONMENT from env (#155)
- Update transaction timeout for order updates to prevent a dabase error (#179)

### Miscellaneous Tasks

- Update patch version to v1.7.2

## [1.7.1] - 2024-08-30

### Bug Fixes

- Revert old refund functions

### Miscellaneous Tasks

- Add comment about legacy api still being needed
- Adding warning log to legacy api controller
- Format
- Bump version to 1.7.1

## [1.7.0] - 2024-08-29

### Features

- Show order endpoint (#171)
- *(api)* Sort orders (#172)
- Change FD price to 0.2 (#177)

### Bug Fixes

- Downgrade google_api_pub_sub

### Miscellaneous Tasks

- Update version to 1.7.0

## [1.6.0] - 2024-08-22

### Features

- Add future-demand fee to bill (#92)

### Miscellaneous Tasks

- Bump version to 1.6.0

## [1.5.6] - 2024-08-15

### Features

- Use correct csv headers for full tickets export
- Add money_formatter
- *(api)* Add ticket admission flag (#167)

### Bug Fixes

- Fix spec definition
- Use personal_information given_name in ticket_pdf_mail.ex
- Remove voucher_id from ticket (#169)

### Miscellaneous Tasks

- Format
- Credo
- Add regex to sanitize csv file name
- Bump version to 1.5.6

## [1.5.5] - 2024-08-08

### Features

- *(ticket-api)* Add hint (#161)
- Add admission to checkin response (#162)

### Miscellaneous Tasks

- Bump to version 1.5.5

## [1.5.4] - 2024-07-17

### Features

- Validate personal information

### Bug Fixes

- Check name for billing address
- Error response

### Refactor

- Simplify address check

### Miscellaneous Tasks

- Formatting
- Only require fistName and lastName
- Update patch version to v1.5.4

## [1.5.3] - 2024-07-16

### Features

- Add elixir action
- Add reviewer lottery (#156)

### Refactor

- Rename order_histroy table to order_histories (#159)

### Miscellaneous Tasks

- Set env to prod
- Cleanup
- Update patch version to v1.5.3

## [1.5.2] - 2024-07-10

### Miscellaneous Tasks

- Bump to version 1.5.2

## [1.5.1] - 2024-07-09

### Bug Fixes

- Change attendee export status check

### Miscellaneous Tasks

- Merge
- Update deps
- Bump version to 1.5.1

## [1.5.0] - 2024-07-05

### Bug Fixes

- Apply pr comments
- Http codes to atoms
- Apply format

### Miscellaneous Tasks

- Formatting | credo | refactoring
- Update Elixir to 1.17.1 (#152)
- Update minor version to v1.5.0

## [1.4.42] - 2024-07-03

### Bug Fixes

- Add 'FAILED' ticket status to ticket history

### Miscellaneous Tasks

- Update patch version to v1.4.42

## [1.4.41] - 2024-07-03

### Features

- Use oban for ticket mail processing
- Use Oban for order confirmation mail

### Bug Fixes

- Re-add mix.lock
- Queue spec

### Miscellaneous Tasks

- Bump elixir-styler (#148)
- Start queue via config
- Change log msg
- Add specs
- Remove comment from Oban config
- Change log msgs
- Add missing indexes to addresses and orders tables
- Update patch version to v1.4.41

## [1.4.40] - 2024-06-19

### Miscellaneous Tasks

- Remove monitoring alter test

## [1.4.39] - 2024-06-19

### Features

- Add configs for elixir-styler

### Bug Fixes

- Expand behaviour
- Remove unused case in Ticket.refund/1
- Removal of an incorrect ticket alias that could lead to code failure

### Testing

- Add some logs to test monitor alerting

### Miscellaneous Tasks

- Run `mix format` with elixir-styler

## [1.4.38] - 2024-06-19

### Miscellaneous Tasks

- Remove monitor alerting test
- Update patch version to v1.4.38

## [1.4.37] - 2024-06-18

### Bug Fixes

- Update Logger config to make service start again

## [1.4.36] - 2024-06-18

### Miscellaneous Tasks

- Update logger lib

## [1.4.35] - 2024-06-18

### Bug Fixes

- Proper pattern match for get_promoter_by_event_id in orderconfirmation mail

### Miscellaneous Tasks

- Bump version to 1.4.35

## [1.4.34] - 2024-06-18

### Bug Fixes

- Remove typo

## [1.4.33] - 2024-06-18

### Testing

- Test monitor alerting

## [1.4.32] - 2024-06-18

### Bug Fixes

- Set ticket.status to failed
- Trim address before save it
- Make trim map entry type safe
- Trim 'region' in addresses before write it into the db

### Miscellaneous Tasks

- Bump patch verion to v1.4.32

## [1.4.31] - 2024-06-13

### Features

- Extend order_confirmation_mail message to send all properties for new mailing implementation

### Miscellaneous Tasks

- Bump version to 1.4.31

## [1.4.30] - 2024-06-13

### Bug Fixes

- Remove total sales api

## [1.4.29] - 2024-06-13

### Bug Fixes

- Add token to total sales api

### Miscellaneous Tasks

- Rename secret
- Update  patch version to v1.4.29

## [1.4.28] - 2024-06-12

### Miscellaneous Tasks

- Add psp_reference to unique index
- Add uniwue constraint to changeset
- Update version to 1.4.28

## [1.4.27] - 2024-06-11

### Refactor

- Remove unused variable

### Miscellaneous Tasks

- Update patch version to v1.4.27

## [1.4.26] - 2024-06-11

### Bug Fixes

- Use Enum.map instead of Enum.reduce

### Miscellaneous Tasks

- Add some error Logs and return a correct error
- Add spec documentation
- Bump patch version to v1.4.26

## [1.4.25] - 2024-06-10

### Bug Fixes

- Default to berlin timezone
- Datetime conversion for NaiveDateTimes
- Typo
- Empty attendee names
- Add better error handling to event finalization
- Add some missing indexes
- Reduce ticket_counter PubSub messages

### Miscellaneous Tasks

- Create run mirrord script to mirror locally orders-service into minikube
- Udpate mix.lock
- Update version to 1.4.25

## [1.4.24] - 2024-06-07

### Bug Fixes

- Add admission flag to ticket

### Miscellaneous Tasks

- Update version to 1.4.24

## [1.4.23] - 2024-06-06

### Features

- Reset status
- Optional hard ticket
- Support hybrid events
- Add admission to tickets
- Extras total
- Add admission to my-tickets
- Add ticket total
- Add redeemed_extras to get_ticket_counter_by_event_id
- Add redeemed_extras to scan API and fix checked_in not to count extras
- Add extrasSold and extrasRemaining to scan API
- Do not show internal_extras_phase name on ticket
- Add spec for count_total_redeemed_extras_for_event_ids
- Add admission check to more queries
- Add venue to ticket email payload

### Bug Fixes

- Create ticket history
- Status date for active tickets
- Find ticket history
- Ticket update count
- Return null if ticket is active
- Me
- Running out of commit msg ideas
- Fix
- Price calc
- Mandatory hard tickets
- Create order address
- Ticket mail address
- Venue name
- Add_if_not_exists
- Another add_if_not_exists
- Adding hard_ticket and admission falgs to create_with_invitation
- Extras total without fees
- Get ticket info from variant
- Admission field
- Use promoter price
- Pattern match with quoted attributes
- Adding maybe_get_voucher to payment process to continue payment with wrong voucher code
- Round discount before applying
- Return min event info
- Add venue
- Add venueId
- Add event id

### Other

- Remove admission flag on variant_id ticket counter

### Miscellaneous Tasks

- Move duplicated function
- Add specs
- Use atom in response status
- Make complex pdf functions public
- Remove donation from extra total
- Update deps
- Change base_total naming
- Change ticketTotal naming
- Refactor
- Remove todo comment
- Mix deps.get
- Move to function
- Cleanup
- Remove tmp var
- Update version to 1.4.23

## [1.4.22] - 2024-05-23

### Features

- Update PR changes
- Add donation Total to carts API
- Add donation preview

### Bug Fixes

- Add environment check to pubsub

### Refactor

- Clean up API

### Miscellaneous Tasks

- Update version to 1.4.22

## [1.4.21] - 2024-05-10

### Bug Fixes

- Count tickets used
- Filter invitation order tickets

### Miscellaneous Tasks

- Remove Dennis from CODEOWNERS
- Update version to 1.4.21

## [1.4.20] - 2024-05-03

### Features

- Add schema to db connection
- Add public as secondary db schema

### Miscellaneous Tasks

- Use ENV instead of Secret
- Bump patch version to v1.4.20

## [1.4.19] - 2024-05-02

### Miscellaneous Tasks

- Set ex_firebase_auth_plug environment to local in dev config
- Change local Phoenix port
- Bump patch version to v1.4.19

## [1.4.18] - 2024-04-26

### Features

- Setting ex_service_client environment via env

### Miscellaneous Tasks

- Bump service_client version to 0.1.48 | bump service version to 1.4.18

## [1.4.17] - 2024-04-25

### Refactor

- Start goth not on local systems

### Miscellaneous Tasks

- Update patch version to v1.4.17

## [1.4.16] - 2024-04-24

### Bug Fixes

- Remove adyen base auth from runtime.exs

### Miscellaneous Tasks

- Update env template
- Update patch version to v1.4.16

## [1.4.15] - 2024-04-23

### Bug Fixes

- Fetching ex_firebase_auth_plug environment param from env

### Miscellaneous Tasks

- Remove minikube config
- Bump ex_firebase_auth version to 0.2.13
- Bump version to 1.4.15

## [1.4.14] - 2024-04-18

### Bug Fixes

- Add user info to ticket history for scaned tickets

### Refactor

- Use TCP/IP DB Connection

### Miscellaneous Tasks

- Update patch version to v1.4.14

## [1.4.13] - 2024-04-18

### Bug Fixes

- Add timezone information to ticket controller

### Miscellaneous Tasks

- Update patch version to v1.4.13

## [1.4.12] - 2024-04-10

### Bug Fixes

- Ticket scan

### Miscellaneous Tasks

- Move swaggerui to /events/api | generate api doc
- Cleanup
- Bump version to 1.4.12

## [1.4.11] - 2024-04-08

### Features

- Add CODEOWNERS
- Send order update event
- Use dto
- Send bill
- Created by order details

### Bug Fixes

- Encode pubsub payload
- Get updated event
- Order import

### Miscellaneous Tasks

- Dont look at this
- Cleanup
- Apply suggestions from code review
- Bump version to 1.4.11

## [1.4.10] - 2024-04-04

### Miscellaneous Tasks

- Remove unused acc from Enum.each
- Bump version to 1.4.10

## [1.4.9] - 2024-04-03

### Features

- Remove old counters vor variants and vouchers
- Add new counters for events service sync
- Remove popular events from orders-service
- Start ticket counter sync by API request
- Send ticket category hint

### Bug Fixes

- Make some bugfixes for events_counter
- Return 0 instead of nil when no tickets are sold
- Make some changes after debugging on fusion

### Miscellaneous Tasks

- After merge cleanup
- Upate mix.lock
- Bump version to 1.3.9

## [1.4.8] - 2024-03-27

### Bug Fixes

- Add order and ticket history for paid orders
- Spelling
- Add updated_at and inserted_at to multi
- Add inserted_at to history changesets
- Remove copy and paste error

### Miscellaneous Tasks

- Bump patch verstion to v1.4.8

## [1.4.7] - 2024-03-26

### Bug Fixes

- Process user_id in Ticket.set_status

### Miscellaneous Tasks

- Bump version to 1.4.7

## [1.4.6] - 2024-03-22

### Bug Fixes

- Change ticket status on cleanup pending orders

### Miscellaneous Tasks

- Update patch version to v1.4.6

## [1.4.5] - 2024-03-21

### Features

- Show unused tickets as previous

### Documentation

- Add a FIXME comment

### Miscellaneous Tasks

- Update patch version to v1.4.5

## [1.4.4] - 2024-03-21

### Bug Fixes

- Put refundet_at back to refunded tickets
- Use ticket.status for checkin validation

### Miscellaneous Tasks

- Update patch version to v1.4.4

## [1.4.3] - 2024-03-21

### Features

- Ticket purchase sync

### Bug Fixes

- Remove purchase_date dependencies
- Repo.one instead of Repo.all for get_order_owner
- Repo.all instead of Repo.one for get_order_owner |  Processing list of owners
- Change handle_event_update_conversation_created back to accept list

### Miscellaneous Tasks

- Pr fixes
- Pr fixes
- Merge feature/SD1-2204/ticket_purchase_sync into fusion
- Bump version to 1.4.3

## [1.4.2] - 2024-03-20

### Bug Fixes

- Use new fallback for PubSub subscription
- Add purchase_date back to tickets for backwarts compatibility

## [1.4.1] - 2024-03-20

### Bug Fixes

- Remove double entries from cloudbuild

### Miscellaneous Tasks

- Update patch version to v1.4.1

## [1.4.0] - 2024-03-20

### Bug Fixes

- Preload personal information
- Add api doc push to cloudbuild
- Preload ticket_history
- Start events.events. pubsub subscriber
- Use Application.get_env correct
- Replace EventsService copy paste with Orders..
- Fetch all PubSub messages
- Remove typo
- Use ticket_status for check_in validation
- Use ticket_status for new tickets

### Miscellaneous Tasks

- Resolve PR change requests
- Update minor version to 1.4.0

## [1.3.8] - 2024-03-11

### Miscellaneous Tasks

- Bump version to 1.3.8

## [1.3.7] - 2024-03-11

### Features

- Attach necessary information
- Preload ticket data
- Add category name to tickets

### Bug Fixes

- Use events service get by id
- Merge conflict
- Name from cat id

### Refactor

- Remove 'is_events-service_deployed' flag

### Miscellaneous Tasks

- Add promoter name
- Apply req changes
- Bump version to 1.3.7

## [1.3.6] - 2024-03-07

### Miscellaneous Tasks

- Clean up
- Update patch version to v1.3.6

## [1.3.5] - 2024-03-07

### Bug Fixes

- Add attendee for each ticket with invitation

### Miscellaneous Tasks

- Update patch version to v1.3.5

## [1.3.4] - 2024-03-04

### Features

- Add ticket category name to my-tickets API
- PR feedback changes
- Add `pay` api doc with seats stuff included
- Remove base image, update elixir

### Bug Fixes

- Add api doc push to remote repo

### Other

- Clean up code, make it more readable

### Miscellaneous Tasks

- Remove unused files
- Use right values for ex_service_client
- Update patch version

## [1.3.3] - 2024-02-23

### Refactor

- *(scan)* Cleanup checked in counter from scan

### Miscellaneous Tasks

- *(admission)* Disable counter for special events
- I am sorry
- Bump version to 1.3.3

## [1.3.2] - 2024-02-22

### Features

- Calculate ticker price for channel
- Add seat_config_id to tickets
- Ignore channels
- Add billing address to order

### Bug Fixes

- Channel config
- Min 0 price
- Remove double order notation
- Send order and ticket mails for accepted invitation
- Do something if all is fine
- Billing address complete check
- Schema prefix
- Postal_code_type
- Is business
- Billing address for invitation order

### Other

- Adding invitation_order check for get_total_amount_checked_in_by_event_id

### Miscellaneous Tasks

- Update ex_service_client to 0.1.35
- Bump version to 1.3.1
- Update ex_service_client to 0.1.43
- Bump version to 1.3.2

## [1.3.1] - 2024-02-15

### Features

- Add billing address to order

### Bug Fixes

- Send order and ticket mails for accepted invitation
- Do something if all is fine
- Billing address complete check
- Schema prefix
- Postal_code_type
- Is business
- Billing address for invitation order

### Miscellaneous Tasks

- Bump version to 1.3.1

## [1.3.0] - 2024-02-09

### Features

- Create InvitationOrder schema and migration
- Do not clean up pending orders with an guestlist invitation
- Create orders with guestlist invitation
- Create an invitation order only with service token
- Upate order for approved or rejected invitation
- Add kickback and tax_rate to bill entity

### Bug Fixes

- Remove copy and paste error
- Do not put stagedates mail addresses to the blacklist
- Check if the mail address is already blacklisted
- Adding , to get_all_order_ids query
- Add missing @
- Add service_token prefix for the pattern match
- Remove copy and paste mistake
- Remove compile warnings

### Refactor

- Clean up InvitationOrder
- Change function name

### Styling

- Clean up code

### Miscellaneous Tasks

- Remove compile warnings
- Update service client
- Update minor version to 1.3.0

## [1.2.14] - 2024-01-29

### Features

- Create order_history

### Miscellaneous Tasks

- Update patch version to v1.2.14

## [1.2.13] - 2024-01-26

### Features

- Create email blacklist

### Miscellaneous Tasks

- Bump patch version to v1.2.13

## [1.2.12] - 2024-01-25

### Features

- Short jwt
- Validate event_id on scan
- Pdf417 image
- Use new ticket token
- Add user document id
- Add ticket total sales  and checked in count

### Bug Fixes

- Generate token
- Scan for old app versions
- Event id
- Fix kickback value

### Miscellaneous Tasks

- Remove pdf417 gen
- Cleanup
- Update description
- Bump ex_service_client to version 1.2.29
- PR changes
- PR changes
- PR changes
- Bump to version 1.2.12

## [1.2.11] - 2024-01-19

### Bug Fixes

- Only one ip

### Miscellaneous Tasks

- Fix type
- Bump version to 1.2.11

## [1.2.10] - 2024-01-19

### Bug Fixes

- *(tickets)* Fix total sales calculation

### Miscellaneous Tasks

- *(release)* Bump version 1.2.10

## [1.2.9] - 2024-01-19

### Features

- Meta pixel tracking on purchase
- Add user_agent and ip

### Bug Fixes

- Value from token
- Value
- Fix
- Remove unused
- Add missing pending and pending refund tickets to variant sold counter

### Miscellaneous Tasks

- Added debug logs
- Bump to version 1.2.9

## [1.2.8] - 2024-01-18

### Features

- Mark orders as fraud and refund the order if all tickets are refunded

### Bug Fixes

- Remove typo

### Miscellaneous Tasks

- Update patch version to v1.2.8

## [1.2.7] - 2024-01-18

### Bug Fixes

- Gcp logs

### Miscellaneous Tasks

- Bump version to 1.2.7

## [1.2.6] - 2024-01-16

### Features

- Add backend_endpoint variable to minikube runtime.exs
- Adding env property
- Adding minikube env to gitignore
- Adding events_url for minikube runtime
- Removing env.minikube.template and adjusting .env.tempalte to be minikube compatible

### Bug Fixes

- Validate merchant

### Miscellaneous Tasks

- Cleanup env.minikube
- Setting runtime envs for minikube
- Bump version to 1.2.6
- Include hackney as dep
- Remove unnecessary code

## [1.2.5] - 2024-01-15

### Other

- Maybe fix validate merchant

## [1.2.4] - 2024-01-15

### Other

- Maybe fix apple pay

## [1.2.3] - 2024-01-15

### Features

- Add 'FRAUD' to oder_status in the PostgresDB

### Bug Fixes

- Attendee name on ticket

### Miscellaneous Tasks

- Update patch version to 1.2.3

## [1.2.2] - 2024-01-15

### Other

- Validate merchant

### Miscellaneous Tasks

- Bump version to 1.2.2

## [1.2.1] - 2024-01-15

### Bug Fixes

- Service client endpoint

## [1.2.0] - 2024-01-15

### Miscellaneous Tasks

- Update version to 1.2.0

## [1.1.7] - 2024-01-12

### Features

- Update service client
- Bump to version 1.1.7

### Miscellaneous Tasks

- Add new routes

## [1.1.6] - 2024-01-11

### Features

- Reset tickets
- Send tickets to paypal mail
- Book seats
- Lock order update
- Lock db on order update
- Book tables/seats
- Shipping fee
- Support unnamed tickets with #count
- Rename shipping ref to hardticket
- Ticket category name on checkin

### Bug Fixes

- Total price calc
- Update email for order
- Order email update
- Wrong paymentMethods amount
- Tmp basic auth fix
- Tmp adyen env fix
- Buy tickets without seat
- Unique seats for general admission tickets
- Compile warning
- Variant_id for order item
- Book seats after payment
- Service client import
- Admin account
- Secure legacy routes

### Other

- Get popular events from db
- Popular event router
- Send as cc
- Add seat
- Book event seats
- Book
- Book table
- Book group
- Rework order processing and validation flow
- Shipping fee

### Miscellaneous Tasks

- Bump seatsio version
- Update seatsio
- Make credo happy
- Add seats dependency
- Update ex_service_client to 0.1.25
- Post merge cleanup
- Remove todos
- Revert config changes
- Add inspect to logs
- Added some more logs
- Formatting
- Bump ex_service client version to 0.1.26
- Bump version to 1.1.6

## [1.1.5] - 2024-01-10

### Bug Fixes

- Remove event update from ticket scan

### Miscellaneous Tasks

- Update mix.lock
- Ubdate patch version to v1.1.5

## [1.1.4] - 2024-01-10

### Bug Fixes

- Revert ex_service_client to 0.1.25

## [1.1.3] - 2024-01-10

### Bug Fixes

- Alias error

### Miscellaneous Tasks

- Update version to 1.1.3

## [1.1.2] - 2024-01-09

### Bug Fixes

- Voucher counter update

### Other

- Update patch version to v1.1.2

## [1.1.1] - 2024-01-09

### Bug Fixes

- Counter update on payment

### Miscellaneous Tasks

- Update version to 1.1.1

## [1.1.0] - 2024-01-09

### Features

- Add purchase_date and categeory_id to tickets API
- Batch counter for variants and voucher for sync update
- Variant and voucher counter pub/sub

### Bug Fixes

- Use ticket instead of order

### Other

- Update minor version to v1.1.0

### Miscellaneous Tasks

- Update mix.lock
- PR change request plus formatting
- PR changes
- PR changes
- PR changes

## [1.0.13] - 2023-12-31

### Bug Fixes

- Remove UUID check

### Other

- Update patch version to v1.0.12
- Update patch version to v.1.0.13

## [1.0.12] - 2023-12-31

### Bug Fixes

- Check event_docuemnt_id if there is no valid UUID for the event

## [1.0.11] - 2023-12-29

### Features

- Remove orders-service-v2
- Resend mails for tickets owner

### Bug Fixes

- Use right pattern match

### Other

- Update patch version to v1.0.11

### Miscellaneous Tasks

- Remove ex_common_db

## [1.0.10] - 2023-12-22

### Bug Fixes

- Unique emai/bcc

### Miscellaneous Tasks

- Update version to 1.0.10

## [1.0.9] - 2023-12-22

### Features

- Implement batch tickets sold counter
- Bump service client version to 0.1.24
- Add thumbnailUrl to ticket email

### Bug Fixes

- *(docker)* Fix docker for gracefull shutdown

### Miscellaneous Tasks

- Update version to 1.0.9

## [1.0.8] - 2023-12-19

### Features

- Add internal carts/totals API
- Reduce calculation for internal use

### Bug Fixes

- *(order)* Change ticket sold handler in payment processing

### Other

- Add carts/totals endpoint for internal use

### Miscellaneous Tasks

- Update ex_service_client
- Update gitignore file
- Bump version to v1.0.8

## [1.0.7] - 2023-12-17

### Features

- Add 'is_events-service_deployed' Feature Flag
- Update tickets endpoints
- Use events-service if it deployed
- Add ticket counter endpoints
- Add bill to ticket list
- Add calculate total sales endpoint
- Add unique index to payin transaction
- Total sold api
- Remove unnecessary API requests,

### Bug Fixes

- Use PhilColums.Seed in all Seeds
- Remove typo
- Add missing comma
- Use EventsService if it is deployed
- Add missing double quotes
- Remove wrong name
- Write ids in the right columns
- Add ex_firebase_auth_plug to runtime.exs
- Add ex_firebase_auth_plug to runtime.exs
- Calculate event total sales only for paid orders
- Use voucher
- Only update tickets sold of old backend
- Update tickets sold
- Add nil to case

### Refactor

- Add some error logs

### Documentation

- Add link to github issue for missing API documentation
- Add fusion reset to README.md

### Miscellaneous Tasks

- Update ex_service_client version to 0.1.10
- Make Config easier to use
- Update ex_service_client
- Update ex_service_client
- Add webhook info logs
- Add inspect to logs
- Add inspect to logs
- Update version to 1.0.7

## [1.0.6] - 2023-12-05

### Features

- Remove "old"/unused bookkeeping, make taxes nullable, it's deprecated now

### Bug Fixes

- Change presale_fee flag from int to boolean
- Use correct presale_fee for cheap tickets

### Other

- Update patch version to v1.0.6

## [1.0.5] - 2023-11-30

### Bug Fixes

- *(checkout)* Typo

### Other

- Update patch version

## [1.0.4] - 2023-11-30

### Features

- Add cloudbuild-v2 for second orders-service instance
- Add countries to database
- Adjust presale and transaction fees from 2023-12-01
- Make /carts/total API backwards compatible

### Bug Fixes

- Resolve pr request changes and make compiler happy again
- Remove mix.env from code
- Add missing promoter_net_total for free tickets
- Remove promoter_bill for free tickets

### Other

- Replace CamelCase with snake_case
- Update patch version to v1.0.4

## [1.0.3] - 2023-11-23

### Other

- Update patch version

### Miscellaneous Tasks

- Resolve pr change requests

## [1.0.2] - 2023-11-16

### Miscellaneous Tasks

- Update patch version

## [1.0.1] - 2023-11-16

### Features

- Send tickets to paypal mail

### Bug Fixes

- Add validFor to my-tickets endpoint
- Total price calc
- Wrong paymentMethods amount
- Update email for order
- Order email update

### Other

- Get popular events from db
- Popular event router

### Miscellaneous Tasks

- Change compile time value
- Change payment env
- Update adyen config
- Bump adyen version
- Update adyen to 0.1.9
- Bump adyen version to 0.1.10
- Update fallback base urls
- Change base_url to backend_endpoint
- Update backend_endpoint url
- Remove log
- Update patch version

## [1.0.0] - 2023-11-13

### Bug Fixes

- Add eventId to my-tickets endpoint

## [0.1.1] - 2023-11-10

### Features

- Add credo
- Add initial migration for orders
- Add esbuild (needed for websockets)
- Init websocket configuration
- Update config, mainly esbuild and database
- Init order schema
- Add ticket schema and migration
- Adapt migrations to new orders-service schema
- Adapt migration to new orders-service schema
- Adjust order/ticket schema
- Create personal_information migration+schema
- Create bill migration+schema
- Create payin_transactions migration+schema
- Create country migration+schema
- Create addresses migration+schema
- Create transaction_history migration+schema
- Adjust address schema according to OpenID
- Add ex_adyen_elixir_api_library
- Add config for ex_adyen_elixir_api_library
- Add health controller
- Add fallback controller
- Add secret module
- *(WIP)* Add /paymentMethods route
- Add CI required files (server/migrate)
- Add openapi documentation
- Add ex_adyen library logic
- Add /orders as prefix to routes
- Add proper secret names for seatsio lib
- Add seatsio lib dependency
- Create /validateMerchant endpoint for drop-in replacement
- Add cleanup pending orders endpoint
- Refund webhook
- Update email-address for an order by given ticket_id
- Validate webhook
- Pay via paypal
- Payment details
- Payin transactions
- Improved error handling
- Fetch_create_workspace for promoter
- Handle mutiple items for same event, category and period
- Send ticket mail
- ...
- Update event after automatic refund
- Send mail for free tickets
- Use /ticket/:id/attendee instead of /ticket/:id
- Change db migrations to make it compatible with the existing data
- Drop in replacement for /my-tickets endpoint
- My tickets
- Simple error handler
- Configs

### Bug Fixes

- Update orders to use utc_datetime instead of date
- Orders migration
- Validate_unique -> unique_constraint
- Adjust ex_adyen_elixir_api_library ENV
- Docker-start.sh exec permissions
- Add necessary env's to docker/env.template
- Precompile secrets.ex in config.exs
- Use File.read instead of File.read!
- Add hostname to runtime.exs and remove IO.inspect
- Remove typo in filename
- Fetch error for double definition of gender_type in postgres db
- Payment methods int parsing
- System.get_env requires binary, not atom
- /paymentMethods argument safe
- Small mix project version info hotfix
- Mix not available after build
- Routing for swaggerui interface
- Remove api_doc pipe from /api endpoint
- Update ex_adyen lib to 0.1.3 (with gh tag)
- Env.template single quote on adyen payments key
- Add adyen lib config to runtime.exs
- Adyen naming and errornous ,
- Authorisation event handler
- Free ticket response
- Duplicate router entries
- Refund handling
- Adyen refund mail
- Typo
- Get secret
- Ticket filter
- Only show paid tickets
- Versioce res fail
- Typo
- Pay with credit card
- App payments
- Use ticket.event_document_id if ticket.event_id is null
- App payment v2
- For sure
- Im blind
- Add double route for /tickets/:id/attendee
- Ensure user authentication on ticket endpoints
- *(app)* Optinal attendees
- Use given_name instead of name
- Remove typo
- *(payment)* Set country code for adyen payments
- *(payment)* Rename country property in billing address
- Warnings
- Oops
- Show attendee if possible
- Remove typo
- Change addresses and orders table

### Other

- Add repo copy to workdir
- Move seeds.ex file into seeds folder
- Adjust runtime.exs database config
- Wrtite `get_secret` without string module
- Just for test, gonna be reverted
- Not reverting previous commit but adding secret path manually again
- Re-fetch secrets during runtime
- Set socket_dir to POSTGRES_HOST
- Phx path /orders
- Set default port to 80 if PHX_PORT not set
- Comment cache_static_manifest out
- Test if delegate fucks missing module up
- Add mix deps.get from hex to dockerfile
- Print ex_adyen lib version
- Add seatsio.ex for basic wrapper
- Pay
- Insert order
- Create tickets
- Pay
- Calc price
- Pay
- Process pending for free tickets
- Make payment
- Init payment
- Calculate cart total endpoint
- Donation amount
- Webhooks
- Support paypal
- Bs algolia replacement
- Disable goth
- Change dev vars
- Add logs
- Print app payments req
- Add my-tickets endpoint
- To float

### Miscellaneous Tasks

- Add various config files
- Add credo config
- .env.template default to dev
- Add ci (#1)
- Update gitignore
- Add run-local script
- Adjust port from 4000 to 4001
- Enable UserSocket
- Update personal_information to OpenID
- Add required env's to template
- Replace get_env with get_secret
- Update deps (maybe fix build error module not found?)
- Remove redundant "orders_service" route
- Move adyen configs to dev.exs
- Update ex_adyen_elixir_api_library
- Remove redundant IO.inspect
- Config stuff
- Update fallback_controller
- Remove IO.inspect
- Mix format
- Update credo + update deps
- Update nested credo setting
- Ex_adyen_.. -> adyen renaming
- Format + add timex
- Cleanup credo
- Fix warning
- Add user_document_id
- Update libs
- Fix merge stuff
- Ass ex_firebase_issuer env
- Debug
- Add base url
- Bump version
- Change default url
- Bump firebase auth plug to 0.2.9
- Use tmp hmac key
- Add default value to webhook pw
- Add logs
- Debug
- Added fallback webhook handler
- Handle all errors
- Update firebase plug
- Add generate pdf topic
- Firebase issuer from secret
- Add todos
- Remove basic auth config
- Debug
- Debug
- Update tickets response
- Update routing
- Add environment config
- Update validFor field
- Bump deps
- Update config
- Cleanup
- Change routes
- Duplicate routes
- Not use voucher if code is not found
- Remove done TODO comment
- Bump adyen version to 0.1.7
- Update service client to 0.1.9
- Only return tickets with events
- Oops again
- Add ticket dates
- Use return url from req
- Update details resp
- Update event with tickets sold
- Remove unused var
- Cleanup
- Cleanup
- Update patch version

<!-- generated by git-cliff -->
