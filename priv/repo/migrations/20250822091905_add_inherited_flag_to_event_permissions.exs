defmodule EventsService.Repo.Migrations.AddInheritedFlagToEventPermissions do
  use Ecto.Migration

  def change do
    alter table(:event_permissions) do
      add :is_inherited_from_seller, :boolean, default: false, null: false
    end

    create index(:event_permissions, [:user_id, :is_inherited_from_seller])
    create index(:event_permissions, [:user_document_id, :is_inherited_from_seller])
  end
end
